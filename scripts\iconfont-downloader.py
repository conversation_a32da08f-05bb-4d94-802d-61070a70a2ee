#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Iconfont 自动下载脚本

由于 iconfont.cn 需要登录和手动操作，此脚本提供自动化指导和验证功能。

使用方法：
1. python3 scripts/iconfont-downloader.py --guide  # 显示下载指导
2. python3 scripts/iconfont-downloader.py --verify # 验证配置
"""

import os
import sys
import json
import argparse
from pathlib import Path

# 项目根目录
ROOT_DIR = Path(__file__).parent.parent
FONTS_DIR = ROOT_DIR / "static" / "fonts"
CSS_FILE = ROOT_DIR / "static" / "css" / "icons.css"

# 需要的图标配置
ICON_CONFIG = {
    "project_name": "任务管理图标",
    "font_family": "iconfont",
    "class_prefix": "icon-",
    "icons": [
        {"name": "wifi", "keywords": ["wifi", "信号", "网络"], "unicode": "\\e7e0"},
        {"name": "location", "keywords": ["位置", "定位", "地图"], "unicode": "\\e651"},
        {"name": "search", "keywords": ["搜索", "查找", "放大镜"], "unicode": "\\e82e"},
        {"name": "arrow-left", "keywords": ["箭头", "返回", "左"], "unicode": "\\e84f"},
        {"name": "close", "keywords": ["关闭", "删除", "叉"], "unicode": "\\e82a"},
        {"name": "menu", "keywords": ["菜单", "更多", "三横线"], "unicode": "\\e006"},
        {"name": "house", "keywords": ["房屋", "家", "住宅"], "unicode": "\\e007"},
        {"name": "building", "keywords": ["建筑", "大楼", "办公"], "unicode": "\\e008"},
        {"name": "clipboard", "keywords": ["剪贴板", "文档", "列表"], "unicode": "\\e009"},
        {"name": "camera", "keywords": ["相机", "拍照", "摄像"], "unicode": "\\e010"},
        {"name": "upload", "keywords": ["上传", "云", "发送"], "unicode": "\\e011"},
        {"name": "check", "keywords": ["勾选", "对号", "完成"], "unicode": "\\e012"},
        {"name": "settings", "keywords": ["设置", "齿轮", "配置"], "unicode": "\\e015"},
        {"name": "refresh", "keywords": ["刷新", "重新加载", "更新"], "unicode": "\\e86b"},
        {"name": "image", "keywords": ["图片", "照片", "相册"], "unicode": "\\e017"},
        {"name": "clock", "keywords": ["时钟", "时间", "计时"], "unicode": "\\e018"},
        {"name": "mobile", "keywords": ["手机", "移动", "电话"], "unicode": "\\e019"},
        {"name": "calendar", "keywords": ["日历", "日期", "时间"], "unicode": "\\e020"},
        {"name": "chart", "keywords": ["图表", "统计", "数据"], "unicode": "\\e021"},
        {"name": "users", "keywords": ["用户组", "团队", "多人"], "unicode": "\\e022"},
        {"name": "user", "keywords": ["用户", "个人", "头像"], "unicode": "\\e023"},
        {"name": "confirm", "keywords": ["确认", "对号", "同意"], "unicode": "\\e024"},
        {"name": "cancel", "keywords": ["取消", "叉号", "拒绝"], "unicode": "\\e025"}
    ]
}

def print_colored(text, color="white"):
    """打印彩色文本"""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m",
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "purple": "\033[95m",
        "cyan": "\033[96m",
        "white": "\033[97m",
        "end": "\033[0m"
    }
    print(f"{colors.get(color, colors['white'])}{text}{colors['end']}")

def show_download_guide():
    """显示下载指导"""
    print_colored("🚀 Iconfont 自动下载指导", "cyan")
    print_colored("=" * 50, "cyan")
    
    print_colored("\n📋 项目配置信息：", "yellow")
    print(f"项目名称: {ICON_CONFIG['project_name']}")
    print(f"字体名称: {ICON_CONFIG['font_family']}")
    print(f"类名前缀: {ICON_CONFIG['class_prefix']}")
    
    print_colored("\n🔗 下载步骤：", "yellow")
    print("1. 访问 https://www.iconfont.cn/")
    print("2. 注册并登录账号")
    print("3. 创建新项目（使用上述配置信息）")
    print("4. 搜索并添加以下图标：")
    
    print_colored("\n📦 需要的图标列表：", "blue")
    print("-" * 60)
    print(f"{'图标名称':<15} {'搜索关键词':<25} {'Unicode'}")
    print("-" * 60)
    
    for icon in ICON_CONFIG["icons"]:
        keywords = ", ".join(icon["keywords"][:3])  # 只显示前3个关键词
        print(f"{icon['name']:<15} {keywords:<25} {icon['unicode']}")
    
    print_colored("\n⬇️ 下载和安装：", "yellow")
    print("5. 选择所有图标后，点击'下载至本地'")
    print("6. 选择 TTF 格式下载")
    print("7. 解压下载的文件")
    print("8. 将 iconfont.ttf 复制到 static/fonts/ 目录")
    print("9. 运行验证脚本: python3 scripts/iconfont-downloader.py --verify")

def verify_installation():
    """验证安装"""
    print_colored("🔍 验证 Iconfont 安装", "cyan")
    print_colored("=" * 30, "cyan")
    
    success = True
    
    # 检查字体文件
    font_path = FONTS_DIR / "iconfont.ttf"
    if font_path.exists():
        size_kb = font_path.stat().st_size / 1024
        print_colored(f"✅ 字体文件存在: {font_path}", "green")
        print(f"   文件大小: {size_kb:.2f} KB")
    else:
        print_colored(f"❌ 字体文件不存在: {font_path}", "red")
        success = False
    
    # 检查CSS文件
    if CSS_FILE.exists():
        print_colored(f"✅ CSS文件存在: {CSS_FILE}", "green")
        
        # 检查图标定义
        with open(CSS_FILE, 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        missing_icons = []
        for icon in ICON_CONFIG["icons"]:
            icon_class = f".icon-{icon['name']}::before"
            if icon_class not in css_content:
                missing_icons.append(icon['name'])
        
        if missing_icons:
            print_colored(f"⚠️  缺少图标定义: {', '.join(missing_icons)}", "yellow")
        else:
            print_colored("✅ 所有图标定义完整", "green")
            
    else:
        print_colored(f"❌ CSS文件不存在: {CSS_FILE}", "red")
        success = False
    
    # 检查目录结构
    if FONTS_DIR.exists():
        print_colored(f"✅ 字体目录存在: {FONTS_DIR}", "green")
    else:
        print_colored(f"❌ 字体目录不存在: {FONTS_DIR}", "red")
        print("   请创建目录: mkdir -p static/fonts")
        success = False
    
    print_colored("\n" + "=" * 30, "cyan")
    if success:
        print_colored("🎉 Iconfont 配置完成！", "green")
    else:
        print_colored("❌ 配置不完整，请按照指导完成设置", "red")
    
    return success

def generate_config_file():
    """生成配置文件"""
    config_file = ROOT_DIR / "iconfont-config.json"
    
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(ICON_CONFIG, f, ensure_ascii=False, indent=2)
    
    print_colored(f"📄 配置文件已生成: {config_file}", "green")

def main():
    parser = argparse.ArgumentParser(description="Iconfont 自动下载和配置工具")
    parser.add_argument("--guide", action="store_true", help="显示下载指导")
    parser.add_argument("--verify", action="store_true", help="验证安装")
    parser.add_argument("--config", action="store_true", help="生成配置文件")
    
    args = parser.parse_args()
    
    if args.guide:
        show_download_guide()
    elif args.verify:
        verify_installation()
    elif args.config:
        generate_config_file()
    else:
        print_colored("🤖 Iconfont 配置工具", "cyan")
        print("\n可用选项:")
        print("  --guide   显示下载指导")
        print("  --verify  验证安装")
        print("  --config  生成配置文件")
        print("\n示例:")
        print("  python3 scripts/iconfont-downloader.py --guide")
        print("  python3 scripts/iconfont-downloader.py --verify")

if __name__ == "__main__":
    main()
