{"name": "omaps-uniapp", "version": "2.0.0", "description": "群忠派单应用", "main": "main.ts", "scripts": {"dev": "echo '开发环境启动，请在HBuilderX中运行'", "build": "echo '构建项目，请在HBuilderX中执行发行操作'", "build:release": "node scripts/build-release.js", "lint": "echo '代码检查'", "test": "echo '运行测试'"}, "keywords": ["uniapp", "vue3", "typescript", "mobile", "派单", "任务管理"], "author": "开发团队", "license": "MIT", "devDependencies": {"@dcloudio/types": "^3.0.0", "@dcloudio/uni-app": "^3.0.0", "@dcloudio/vite-plugin-uni": "^0.0.1", "typescript": "^4.0.0", "vite": "^4.0.0"}, "dependencies": {"pinia": "^2.0.0", "vue": "^3.0.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "项目仓库地址"}, "bugs": {"url": "问题反馈地址"}, "homepage": "项目主页"}