// Basic认证信息
export const AUTH_BASIC : string = 'Basic MTRhOWNmNzk3OTMxNDMwODk2YWQxM2E2YjE4NTU2MTE6YTA1ZmUxZmM1MGVkNDJhNDk5MGM2YzZmYzRiZWMzOTg=';

// 环境配置
const ENV = {
  // 开发环境
  development: {
    API_URL: 'http://192.168.100.89:8848',
    OSS_URL: 'https://minio.omaps.cn'
  },
  // 生产环境
  production: {
    API_URL: 'https://api.omaps.cn',
    OSS_URL: 'https://minio.omaps.cn'
  },
  // 测试环境
  test: {
    API_URL: 'http://test-api.omaps.cn',
    OSS_URL: 'https://test-minio.omaps.cn'
  }
};

// 当前环境 (可以根据需要修改)
const CURRENT_ENV: keyof typeof ENV = 'production'; // 'development' | 'production' | 'test'

// API地址
export const API_URL: string = ENV[CURRENT_ENV].API_URL;

// OSS地址
export const OSS_URL: string = ENV[CURRENT_ENV].OSS_URL;

// 版本
export const VERSION : string = '2.0.0';

// 导出当前环境信息
export const ENVIRONMENT = {
  current: CURRENT_ENV,
  isDevelopment: CURRENT_ENV === 'development',
  isProduction: CURRENT_ENV === 'production',
  isTest: CURRENT_ENV === 'test'
};

// 打印当前环境信息（仅在非生产环境）
if (CURRENT_ENV !== 'production') {
  console.info(`🌍 [ENV] 当前环境: ${CURRENT_ENV}`);
  console.info(`🌍 [ENV] API地址: ${API_URL}`);
  console.info(`🌍 [ENV] OSS地址: ${OSS_URL}`);
}
