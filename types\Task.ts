import { TaskImages } from "./TaskImages";

/** OMaps —— Task, 上刊任务信息 */
export interface Task {
	hasTaken: number;
	/** 任务Id */
	taskId?: string;
	/** 关联派单 */
	deliveryId?: string;
	/** 所属小区 */
	zoneId?: number;
	/** 关联点位 */
	spotId?: number;
	/** 上刊开始日期 */
	beginDay?: string;
	/** 上刊结束日期 */
	endDay?: string;
	/** 实景位置类型 */
	zonePhotoType?: number;
	/** 实景位置 */
	zonePhotoAddress?: string;
	/** 拍照数量要求，每个任务需要拍摄照片的数量 */
	photoMax?: number;
	/** 仅允许拍照，是否仅允许拍照上传，即不允许从手机相册选择照片上传 */
	onlyPhoto?: number;
	/** 所属区 */
	belongDistrict?: string;
	/** 位置描述/详细地址 */
	zoneAddress?: string;
	/** 项目名称/楼盘名称 */
	zoneName?: string;
	/** 点位编号 */
	spotCode?: string;
	/** 广告楼层 */
	floorNo?: string;
	/** 单元号 */
	apartNo?: string;
	/** 电梯号 */
	elevatorNo?: string;
	/** 广告位置 */
	position?: string;
	/** 任务状态 */
	taskStatus?: number | string;

	/** 点位最后一次上画内容图片 */
	previousContentImageUrl?: string;
	/** 本次上画内容图片 */
	currentContentImageUrl?: string;

	/** 折叠相同位置的其他任务 */
	collapsedZoneTasks?: Task[];
}