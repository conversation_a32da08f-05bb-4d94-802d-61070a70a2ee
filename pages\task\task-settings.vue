<template>
  <view class="filter-dialog" :style="dynamicFontStyle">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <view class="dialog-header">
      <view class="back-button" @click="goBack">
        <text class="iconfont icon-back"></text>
      </view>
      <view class="title">任务显示设置</view>
      <view class="reset-button" @click="resetSettings">
        <text>重置设置</text>
      </view>
    </view>

    <view class="filter-section">
      <view class="section-title">任务类型</view>
      <radio-group @change="onTaskTypeChange">
        <view class="option-list">
          <view class="option-item" @click="selectTaskType('all')">
            <radio value="all" :checked="taskType === 'all'" color="#2196F3" />
            <text class="option-text">全部任务</text>
          </view>
          <view class="option-item" @click="selectTaskType('spot')">
            <radio value="spot" :checked="taskType === 'spot'" color="#2196F3" />
            <text class="option-text">仅点位任务</text>
          </view>
          <view class="option-item" @click="selectTaskType('zone')">
            <radio value="zone" :checked="taskType === 'zone'" color="#2196F3" />
            <text class="option-text">仅实景任务</text>
          </view>
        </view>
      </radio-group>
      <view class="description-section">
        <text class="description-item">点位任务：广告框架等固定点位的拍照任务</text>
        <text class="description-item">实景任务：住宅区域等实景环境的拍照任务</text>
      </view>
    </view>

    <view class="filter-section">
      <view class="section-title">完成状态</view>
      <radio-group @change="onCompletionStatusChange">
        <view class="option-list">
          <view class="option-item" @click="selectCompletionStatus('all')">
            <radio value="all" :checked="completionStatus === 'all'" color="#2196F3" />
            <text class="option-text">全部</text>
          </view>
          <view class="option-item" @click="selectCompletionStatus('notComplete')">
            <radio value="notComplete" :checked="completionStatus === 'notComplete'" color="#2196F3" />
            <text class="option-text">仅显示未完成</text>
          </view>
          <view class="option-item" @click="selectCompletionStatus('complete')">
            <radio value="complete" :checked="completionStatus === 'complete'" color="#2196F3" />
            <text class="option-text">仅显示已完成</text>
          </view>
        </view>
      </radio-group>
    </view>

    <view class="filter-section">
      <view class="section-title">拍照状态</view>
      <view class="option-list">
        <view class="option-item" @click="selectPhotoStatus('all')">
          <radio :checked="photoStatus === 'all'" color="#2196F3" />
          <text class="option-text">全部</text>
        </view>
        <view class="option-item" @click="selectPhotoStatus('notTaken')">
          <radio :checked="photoStatus === 'notTaken'" color="#2196F3" />
          <text class="option-text">仅显示未拍照</text>
        </view>
        <view class="option-item" @click="selectPhotoStatus('taken')">
          <radio :checked="photoStatus === 'taken'" color="#2196F3" />
          <text class="option-text">仅显示已拍照</text>
        </view>
        <view class="option-item" @click="selectPhotoStatus('notComplete')">
          <radio :checked="photoStatus === 'notComplete'" color="#2196F3" />
          <text class="option-text">仅显示未拍照完成</text>
        </view>
        <view class="option-item" @click="selectPhotoStatus('complete')">
          <radio :checked="photoStatus === 'complete'" color="#2196F3" />
          <text class="option-text">仅显示已拍照完成</text>
        </view>
      </view>

      <view class="description-section">
        <text class="description-item">未拍照：在上传队列中没有待上传的照片</text>
        <text class="description-item">已拍照：在上传队列中有待上传的照片</text>
        <text class="description-item">未拍照完成：在上传队列中的照片数量小于要求的拍照数量</text>
        <text class="description-item">已拍照完成：在上传队列中的照片数量大于等于要求的拍照数量</text>
      </view>
    </view>

    <view class="filter-section">
      <view class="section-title">合并实景任务</view>
      <view class="option-list">
        <view class="option-item" @click="selectMergeZoneTasks(true)">
          <radio :checked="mergeZoneTasks === true" color="#2196F3" />
          <text class="option-text">是</text>
        </view>
        <view class="option-item" @click="selectMergeZoneTasks(false)">
          <radio :checked="mergeZoneTasks === false" color="#2196F3" />
          <text class="option-text">否</text>
        </view>
      </view>
      <view class="description-section">
        <text class="description-item">相同位置的实景任务并为一个任务显示，需要注意拍照要求是否一致</text>
      </view>
    </view>

    <view class="filter-section">
      <view class="section-title">任务排列方式</view>
      <view class="option-list">
        <view class="option-item" @click="selectTaskSortMode('mixed')">
          <radio :checked="taskSortMode === 'mixed'" color="#2196F3" />
          <text class="option-text">实景与点位任务混排</text>
        </view>
        <view class="option-item" @click="selectTaskSortMode('zoneFirst')">
          <radio :checked="taskSortMode === 'zoneFirst'" color="#2196F3" />
          <text class="option-text">先实景再点位任务</text>
        </view>
        <view class="option-item" @click="selectTaskSortMode('spotFirst')">
          <radio :checked="taskSortMode === 'spotFirst'" color="#2196F3" />
          <text class="option-text">先点位再实景任务</text>
        </view>
      </view>
      <view class="description-section">
        <text class="description-item">任务排列方式：决定任务列表中实景任务和点位任务的排序方式</text>
        <text class="description-item">地址层级：小区 > 楼层 > 单元 > 电梯 > 位置 > 点位</text>
      </view>
    </view>

  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, onBeforeUnmount, computed } from 'vue';
import { useSettingsStore, defaultSettings } from '../../stores/settings';
import { showSuccessToast } from '../../utils/toast';

// 声明全局 uni 对象和函数，解决 TypeScript 错误
declare const uni: any;
declare const getCurrentPages: () => any[];

// 获取设置 store
const settingsStore = useSettingsStore();

// 状态栏高度
const statusBarHeight = ref(20);

// 字体大小管理
const currentFontScale = ref(1);

// 动态字体样式
const dynamicFontStyle = computed(() => ({
  '--font-scale': currentFontScale.value
}));

// 初始化字体大小
const initFontSize = () => {
  try {
    const savedFontScale = uni.getStorageSync('fontScale') || 1;
    currentFontScale.value = savedFontScale;
    console.info(`📄 [TASK-SETTINGS] 页面字体大小初始化: 缩放 ${savedFontScale}`);
  } catch (error) {
    console.error('页面字体大小初始化失败:', error);
  }
};

// 处理字体大小变化
const handleFontSizeChange = (data: any) => {
  const { size, scale } = data;
  currentFontScale.value = scale;
  console.info(`📄 [TASK-SETTINGS] 页面字体大小已更新: ${size} (缩放: ${scale})`);
};

// 任务类型
const taskType = ref(settingsStore.settings.taskDisplaySettings.taskType || 'all');
// 拍照状态
const photoStatus = ref(settingsStore.settings.taskDisplaySettings.photoStatus);
// 完成状态
const completionStatus = ref(settingsStore.settings.taskDisplaySettings.completionStatus);
// 合并实景任务
const mergeZoneTasks = ref(settingsStore.settings.taskDisplaySettings.mergeZoneTasks);
// 任务排列方式
const taskSortMode = ref(settingsStore.settings.taskDisplaySettings.taskSortMode);

// 选择任务类型
const selectTaskType = (type: string) => {
  taskType.value = type;
};

// radio-group change事件处理
const onTaskTypeChange = (e: any) => {
  taskType.value = e.detail.value;
};

// 选择拍照状态
const selectPhotoStatus = (status: string) => {
  photoStatus.value = status;
};

// 选择完成状态
const selectCompletionStatus = (status: string) => {
  completionStatus.value = status;
};

// radio-group change事件处理
const onCompletionStatusChange = (e: any) => {
  completionStatus.value = e.detail.value;
};

// 选择是否合并实景任务
const selectMergeZoneTasks = (merge: boolean) => {
  mergeZoneTasks.value = merge;
};

// 选择任务排列方式
const selectTaskSortMode = (mode: string) => {
  taskSortMode.value = mode;
};

// 获取事件通道
const getEventChannel = () => {
  try {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    return currentPage.getOpenerEventChannel();
  } catch (error) {
    console.error('获取事件通道失败:', error);
    return null;
  }
};

// 保存设置到 store
const saveSettingsToStore = () => {
  try {
    // 更新 store 中的任务显示设置
    settingsStore.settings.taskDisplaySettings = {
      taskType: taskType.value,
      photoStatus: photoStatus.value,
      completionStatus: completionStatus.value,
      mergeZoneTasks: mergeZoneTasks.value,
      taskSortMode: taskSortMode.value
    };

    // 保存设置到本地存储
    settingsStore.saveSettings();
    console.info('设置已保存到 store');
  } catch (error) {
    console.error('保存设置到 store 失败:', error);
  }
};

// 关闭时应用设置
const applySettingsOnClose = () => {
  try {
    // 创建设置对象
    const settings = {
      taskType: taskType.value,
      photoStatus: photoStatus.value,
      completionStatus: completionStatus.value,
      mergeZoneTasks: mergeZoneTasks.value,
      taskSortMode: taskSortMode.value
    };

    // 保存设置到 store
    saveSettingsToStore();

    // 通过事件通道发送设置
    const eventChannel = getEventChannel();
    if (eventChannel) {
      eventChannel.emit('settingsResult', settings);
    }

    console.info('设置已应用:', settings);
  } catch (error) {
    console.error('应用设置失败:', error);
  }
};

// 重置设置
const resetSettings = () => {
  // 使用 settings store 中导出的默认值，确保一致性
  const defaultTaskDisplaySettings = defaultSettings.taskDisplaySettings;

  // 重置所有设置为默认值
  taskType.value = defaultTaskDisplaySettings.taskType || 'all';
  photoStatus.value = defaultTaskDisplaySettings.photoStatus;
  completionStatus.value = defaultTaskDisplaySettings.completionStatus;
  mergeZoneTasks.value = defaultTaskDisplaySettings.mergeZoneTasks;
  taskSortMode.value = defaultTaskDisplaySettings.taskSortMode;

  // 显示提示
  showSuccessToast('已重置为默认设置');
};

// 返回上一页
const goBack = () => {
  // 在返回前应用设置
  applySettingsOnClose();
  uni.navigateBack();
};

// 页面加载时，获取当前设置
onMounted(() => {
  // 获取状态栏高度
  try {
    const sysInfo = uni.getSystemInfoSync();
    statusBarHeight.value = sysInfo.statusBarHeight || 20;
  } catch (error) {
    console.warn('获取状态栏高度失败:', error);
    statusBarHeight.value = 20; // 使用默认值
  }

  // 初始化字体大小
  initFontSize();

  // 监听字体大小变化
  uni.$on('fontSizeChanged', handleFontSizeChange);

  // 延迟一下，确保页面已经准备好
  setTimeout(() => {
    try {
      // 尝试从事件通道获取当前会话的设置
      const eventChannel = getEventChannel();
      if (eventChannel) {
        eventChannel.on('currentSettings', (data: any) => {
          if (data) {
            // 应用从事件通道获取的设置
            if (data.taskType) {
              taskType.value = data.taskType;
            }
            if (data.photoStatus) {
              photoStatus.value = data.photoStatus;
            }
            if (data.completionStatus) {
              completionStatus.value = data.completionStatus;
            }
            if (data.mergeZoneTasks !== undefined) {
              mergeZoneTasks.value = data.mergeZoneTasks;
            }
            if (data.taskSortMode) {
              taskSortMode.value = data.taskSortMode;
            }

            // 保存设置到 store
            saveSettingsToStore();
          }
        });
      }
    } catch (error) {
      console.error('监听设置事件失败:', error);
    }
  }, 100);
});

// 页面卸载前应用设置（处理手势关闭等情况）
onBeforeUnmount(() => {
  applySettingsOnClose();
});

// 页面卸载时的清理
onUnmounted(() => {
  // 清理字体大小监听
  uni.$off('fontSizeChanged', handleFontSizeChange);
  console.info('任务设置页面已卸载');
});
</script>

<style lang="scss" scoped>
.filter-dialog {
  padding: 0 0 30px 0;
  background-color: #f5f5f5;
  min-height: 100vh;

  // 应用字体缩放到所有文本元素
  .title, .reset-button, .section-title, .option-text, .description-item {
    font-size: calc(1em * var(--font-scale, 1)) !important;
  }

  .status-bar {
    background-color: #2196F3;
    width: 100%;
  }

  .dialog-header {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background-color: #2196F3;
    color: #FFFFFF;
    height: 50px;
    position: sticky;
    top: 0;
    z-index: 100;

    .back-button {
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;

      .iconfont {
        font-size: 16px;
        color: #FFFFFF;
        transform: scale(1.1);
      }
    }

    .title {
      flex: 1;
      text-align: center;
      font-size: calc(18px * var(--font-scale, 1));
      font-weight: bold;
    }

    .reset-button {
      font-size: calc(14px * var(--font-scale, 1));
      color: #FFFFFF;
      opacity: 0.9;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .filter-section {
    margin: 15px;
    background-color: #FFFFFF;
    border-radius: 8px;
    overflow: hidden;

    .section-title {
      padding: 15px;
      font-size: calc(16px * var(--font-scale, 1));
      font-weight: bold;
      border-bottom: 1px solid #f0f0f0;
    }

    .option-list {
      .option-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s ease;
        cursor: pointer;

        &:hover {
          background-color: #f8f8f8;
        }

        &:active {
          background-color: #f0f0f0;
        }

        .option-text {
          margin-left: 10px;
          font-size: calc(16px * var(--font-scale, 1));
          transition: color 0.2s ease;
        }
      }
    }

    .description-section {
      padding: 15px;
      background-color: #f9f9f9;

      .description-item {
        display: block;
        font-size: calc(12px * var(--font-scale, 1));
        color: #666;
        margin-bottom: 5px;
      }
    }
  }
}
</style>
