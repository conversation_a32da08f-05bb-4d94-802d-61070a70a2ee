/* Iconfont 图标样式 - 适用于 Android App */

/* 引入 iconfont 字体 */
@font-face {
  font-family: 'iconfont';
  src: url('../fonts/iconfont.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

/* 基础 iconfont 样式 */
.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  display: inline-block;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
}

/* 兼容 unicode-icon 类名 */
.unicode-icon {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  display: inline-block;
  text-align: center;
}

/* 图标大小 */
.iconfont.small, .unicode-icon.small { font-size: 14px; }
.iconfont.normal, .unicode-icon.normal { font-size: 16px; }
.iconfont.large, .unicode-icon.large { font-size: 20px; }
.iconfont.xl, .unicode-icon.xl { font-size: 24px; }
.iconfont.x2, .unicode-icon.x2 { font-size: 32px; }

/* 图标颜色 */
.iconfont.primary, .unicode-icon.primary { color: #007AFF; }
.iconfont.success, .unicode-icon.success { color: #67C23A; }
.iconfont.warning, .unicode-icon.warning { color: #E6A23C; }
.iconfont.danger, .unicode-icon.danger { color: #F56C6C; }
.iconfont.info, .unicode-icon.info { color: #909399; }
.iconfont.white, .unicode-icon.white { color: #FFFFFF; }

/* WiFi 状态颜色 */
.iconfont.wifi-online, .unicode-icon.wifi-online { color: #67C23A; }
.iconfont.wifi-offline, .unicode-icon.wifi-offline { color: #F56C6C; }
.iconfont.wifi-connecting, .unicode-icon.wifi-connecting { color: #E6A23C; }

/* 图标类名定义 - 需要根据实际下载的 iconfont 更新 Unicode 编码 */
/** Tabs Page - 根据实际iconfont文件更新 */
.icon-delivery::before { content: "\e65a"; }    /* 派单图标 */
.icon-task::before { content: "\e610"; }        /* 任务图标 */
.icon-queue::before { content: "\e9e3"; }       /* 消息队列图标 */
.icon-my::before { content: "\e608"; }          /* 我的图标 */

/** Vue Pages - 根据实际iconfont文件自动更新 */
.icon-wifi::before { content: "\e7e0"; }        /* WiFi图标 */
.icon-search::before { content: "\e6e1"; }      /* 搜索图标 */
.icon-close::before { content: "\e6dc"; }       /* 关闭图标 */
.icon-more::before { content: "\e86c"; }        /* 更多菜单 */
.icon-house::before { content: "\e88b"; }       /* 房屋图标 - 实景任务 */
.icon-point::before { content: "\e634"; }       /* 点位图标 */
.icon-clipboard::before { content: "\e65a"; }   /* 剪贴板 - 派单图标 */
.icon-camera::before { content: "\e637"; }      /* 相机拍照图标 */
.icon-album::before { content: "\e618"; }       /* 相册图标 */
.icon-settings::before { content: "\e86c"; }    /* 设置图标 */
.icon-refresh::before { content: "\e86b"; }     /* 刷新图标 */
.icon-exchange::before { content: "\e7db"; }    /* 交换图标 */
.icon-calendar::before { content: "\e81a"; }    /* 日历图标 */
.icon-chart::before { content: "\e790"; }       /* 图表图标 */
.icon-customers::before { content: "\e7d3"; }   /* 客户图标 */
.icon-confirm::before { content: "\e82b"; }     /* 确认图标 */
.icon-cancel::before { content: "\e82a"; }      /* 取消图标 */
.icon-back::before { content: "\e84f"; }        /* 返回按钮 */
.icon-check::before { content: "\e82b"; }       /* 勾选图标 */
.icon-clock::before { content: "\e819"; }       /* 时钟图标 */
.icon-navigation::before { content: "\e651"; }  /* 导航图标 */

/* 图标别名定义 - 为了兼容Vue文件中的使用 */
.icon-history::before { content: "\e81a"; }     /* 历史记录 - 使用日历图标 */
.icon-attendance::before { content: "\e790"; }  /* 考勤 - 使用图表图标 */
.icon-customer::before { content: "\e7d3"; }    /* 客户 - 使用用户组图标 */
.icon-emoji::before { content: "\e608"; }       /* 表情 - 使用我的图标 */

/* 导航图标样式 */
.icon-navigation {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: #007AFF;
  font-size: 16px;
  cursor: pointer;
}

.icon-navigation:active {
  opacity: 0.7;
}

/* 空状态图标 */
.empty-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  font-size: 48px;
  color: #C0C4CC;
}
