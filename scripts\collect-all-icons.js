#!/usr/bin/env node

/**
 * 收集所有Vue文件中使用的图标脚本
 *
 * 功能：
 * 1. 扫描所有Vue文件中的图标使用情况
 * 2. 生成完整的图标列表
 * 3. 更新icons.css文件
 */

const fs = require('fs');
const path = require('path');

// 项目根目录
const ROOT_DIR = path.resolve(__dirname, '..');
const CSS_FILE = path.join(ROOT_DIR, 'static', 'css', 'icons.css');

console.log('🔍 收集所有Vue文件中的图标使用情况');
console.log('================================');

// 递归查找Vue文件
function findAllVueFiles() {
  const vueFiles = [];

  function scanDirectory(dir, relativePath = '') {
    try {
      const items = fs.readdirSync(dir);

      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const relativeFilePath = path.join(relativePath, item);

        try {
          const stat = fs.statSync(fullPath);

          if (stat.isDirectory()) {
            // 跳过一些不需要的目录
            if (!['node_modules', '.git', 'dist', 'build', '.nuxt'].includes(item)) {
              scanDirectory(fullPath, relativeFilePath);
            }
          } else if (stat.isFile() && item.endsWith('.vue')) {
            vueFiles.push(relativeFilePath);
          }
        } catch (error) {
          // 忽略无法访问的文件/目录
        }
      });
    } catch (error) {
      // 忽略无法访问的目录
    }
  }

  // 扫描主要目录
  const dirsToScan = ['pages', 'components', 'subpages', 'uni_modules'];

  dirsToScan.forEach(dirName => {
    const dirPath = path.join(ROOT_DIR, dirName);
    if (fs.existsSync(dirPath)) {
      scanDirectory(dirPath, dirName);
    }
  });

  return vueFiles;
}

// 扫描Vue文件中使用的图标
function scanAllIcons() {
  const vueFiles = findAllVueFiles();
  const usedIcons = new Map(); // 使用Map来记录使用位置

  console.log(`📁 找到 ${vueFiles.length} 个Vue文件\n`);

  vueFiles.forEach(filePath => {
    const fullPath = path.join(ROOT_DIR, filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');

      // 查找 icon-xxx 类名
      const iconRegex = /icon-([a-zA-Z-]+)/g;
      let match;
      const fileIcons = new Set();

      while ((match = iconRegex.exec(content)) !== null) {
        const iconName = match[1];
        fileIcons.add(iconName);

        if (!usedIcons.has(iconName)) {
          usedIcons.set(iconName, []);
        }
        usedIcons.get(iconName).push(filePath);
      }

      if (fileIcons.size > 0) {
        console.log(`📄 ${filePath}: ${fileIcons.size} 个图标`);
        fileIcons.forEach(icon => {
          console.log(`   - icon-${icon}`);
        });
      }
    }
  });

  return usedIcons;
}

// 读取当前CSS中的图标定义
function readCurrentIcons() {
  if (!fs.existsSync(CSS_FILE)) {
    console.log('❌ icons.css 文件不存在');
    return {};
  }

  const content = fs.readFileSync(CSS_FILE, 'utf8');
  const iconRegex = /\.icon-([^:]+)::before\s*{\s*content:\s*"\\([^"]+)"/g;
  const icons = {};

  let match;
  while ((match = iconRegex.exec(content)) !== null) {
    const iconName = match[1];
    const unicode = match[2];
    icons[iconName] = unicode;
  }

  return icons;
}

// 读取iconfont字体文件中的图标映射
function readFontIcons() {
  const fontCssPath = path.join(ROOT_DIR, 'static', 'fonts', 'iconfont.css');
  if (!fs.existsSync(fontCssPath)) {
    console.log('❌ iconfont.css 文件不存在');
    return {};
  }

  const content = fs.readFileSync(fontCssPath, 'utf8');
  const iconRegex = /\.icon-([^:]+):before\s*{\s*content:\s*"\\([^"]+)"/g;
  const fontIcons = {};

  let match;
  while ((match = iconRegex.exec(content)) !== null) {
    const iconName = match[1];
    const unicode = match[2];
    fontIcons[iconName] = unicode;
  }

  return fontIcons;
}

// 生成图标映射建议
function generateIconMapping() {
  const usedIcons = scanAllIcons();
  const currentIcons = readCurrentIcons();
  const fontIcons = readFontIcons();

  console.log('\n📊 图标使用统计');
  console.log('================================');
  console.log(`实际使用的图标: ${usedIcons.size}`);
  console.log(`已定义的图标: ${Object.keys(currentIcons).length}`);
  console.log(`字体中的图标: ${Object.keys(fontIcons).length}`);

  // 分析图标状态
  const iconStatus = {};

  // 检查使用的图标
  usedIcons.forEach((files, iconName) => {
    iconStatus[iconName] = {
      used: true,
      files: files,
      defined: !!currentIcons[iconName],
      unicode: currentIcons[iconName] || null,
      fontAvailable: false,
      suggestedUnicode: null
    };

    // 查找字体中的匹配图标
    const fontMatch = findBestFontMatch(iconName, fontIcons);
    if (fontMatch) {
      iconStatus[iconName].fontAvailable = true;
      iconStatus[iconName].suggestedUnicode = fontMatch.unicode;
      iconStatus[iconName].fontIconName = fontMatch.name;
    }
  });

  // 检查未使用的已定义图标
  Object.keys(currentIcons).forEach(iconName => {
    if (!usedIcons.has(iconName)) {
      iconStatus[iconName] = {
        used: false,
        files: [],
        defined: true,
        unicode: currentIcons[iconName],
        fontAvailable: false,
        suggestedUnicode: null
      };
    }
  });

  return iconStatus;
}

// 查找最佳字体图标匹配
function findBestFontMatch(iconName, fontIcons) {
  // 直接匹配
  if (fontIcons[iconName]) {
    return { name: iconName, unicode: fontIcons[iconName] };
  }

  // 映射表
  const iconMapping = {
    'wifi': 'wifi',
    'search': 'search1',
    'close': 'close',
    'more': 'menu',
    'house': 'home',
    'point': 'kuangjia',
    'clipboard': 'CombinedShape',
    'camera': 'paizhao',
    'album': 'xiangce',
    'settings': 'setting',
    'refresh': 'sync-alt',
    'exchange': 'exchange-fill',
    'calendar': 'calendar-alt',
    'chart': 'chart-line',
    'customers': 'user-group-fill',
    'confirm': 'check',
    'cancel': 'times',
    'delivery': 'CombinedShape',
    'task': 'task',
    'queue': 'xiaoxiduilie',
    'my': 'wode',
    'back': 'angle-left',
    'navigation': 'daohang'
  };

  const mappedName = iconMapping[iconName];
  if (mappedName && fontIcons[mappedName]) {
    return { name: mappedName, unicode: fontIcons[mappedName] };
  }

  return null;
}

// 显示图标状态报告
function displayIconReport(iconStatus) {
  console.log('\n📋 图标状态报告');
  console.log('================================');

  const sortedIcons = Object.keys(iconStatus).sort();

  sortedIcons.forEach(iconName => {
    const status = iconStatus[iconName];

    if (status.used) {
      const statusIcon = status.defined ? '✅' : '❌';
      const fontIcon = status.fontAvailable ? '🎨' : '⚠️';

      console.log(`${statusIcon} ${fontIcon} icon-${iconName.padEnd(15)}`);
      console.log(`     使用文件: ${status.files.join(', ')}`);

      if (status.defined) {
        console.log(`     当前编码: \\${status.unicode}`);
      } else {
        console.log(`     状态: 未定义`);
      }

      if (status.fontAvailable) {
        console.log(`     建议编码: \\${status.suggestedUnicode} (${status.fontIconName})`);
      } else {
        console.log(`     建议: 需要从iconfont下载`);
      }
      console.log('');
    } else {
      console.log(`🗑️  icon-${iconName.padEnd(15)} - 未使用，可删除`);
    }
  });
}

// 生成更新后的CSS内容
function generateUpdatedCSS(iconStatus) {
  console.log('\n🔧 生成更新的CSS内容');
  console.log('================================');

  // 读取当前CSS文件
  const currentCSS = fs.readFileSync(CSS_FILE, 'utf8');

  // 提取CSS头部（到图标定义之前的部分）
  const headerMatch = currentCSS.match(/([\s\S]*?)\/\* 图标类名定义/);
  const header = headerMatch ? headerMatch[1] : '';

  // 生成新的图标定义
  const usedIcons = Object.keys(iconStatus)
    .filter(iconName => iconStatus[iconName].used)
    .sort();

  let iconDefinitions = '/* 图标类名定义 - 根据Vue文件使用情况自动生成 */\n';

  // 按类别分组
  const categories = {
    'Tabs Page': ['delivery', 'task', 'queue', 'my'],
    'Navigation': ['wifi', 'search', 'close', 'more', 'settings', 'refresh', 'back'],
    'Content': ['house', 'point', 'clipboard', 'camera', 'album'],
    'Actions': ['exchange', 'calendar', 'chart', 'customers', 'confirm', 'cancel'],
    'Others': []
  };

  // 将图标分配到类别
  const categorizedIcons = { ...categories };
  usedIcons.forEach(iconName => {
    let assigned = false;
    Object.keys(categories).forEach(category => {
      if (categories[category].includes(iconName)) {
        assigned = true;
      }
    });
    if (!assigned) {
      categorizedIcons['Others'].push(iconName);
    }
  });

  // 生成分类的图标定义
  Object.keys(categorizedIcons).forEach(category => {
    const categoryIcons = categorizedIcons[category].filter(icon => usedIcons.includes(icon));
    if (categoryIcons.length > 0) {
      iconDefinitions += `\n/** ${category} */\n`;
      categoryIcons.forEach(iconName => {
        const status = iconStatus[iconName];
        const unicode = status.suggestedUnicode || status.unicode || 'e000';
        const comment = getIconComment(iconName);
        iconDefinitions += `.icon-${iconName}::before { content: "\\${unicode}"; }${comment}\n`;
      });
    }
  });

  // 生成别名定义
  const aliases = {
    'history': 'calendar',
    'attendance': 'chart',
    'customer': 'customers',
    'emoji': 'my'
  };

  iconDefinitions += '\n/* 图标别名定义 - 为了兼容Vue文件中的使用 */\n';
  Object.keys(aliases).forEach(alias => {
    const target = aliases[alias];
    if (usedIcons.includes(alias) && iconStatus[target]) {
      const unicode = iconStatus[target].suggestedUnicode || iconStatus[target].unicode;
      iconDefinitions += `.icon-${alias}::before { content: "\\${unicode}"; }     /* ${alias} - 使用${target}图标 */\n`;
    }
  });

  return header + iconDefinitions + '\n' + getCSSFooter();
}

// 获取图标注释
function getIconComment(iconName) {
  const comments = {
    'delivery': '    /* 派单图标 */',
    'task': '        /* 任务图标 */',
    'queue': '       /* 消息队列图标 */',
    'my': '          /* 我的图标 */',
    'wifi': '        /* WiFi图标 */',
    'search': '      /* 搜索图标 */',
    'close': '       /* 关闭图标 */',
    'more': '        /* 更多菜单 */',
    'house': '       /* 房屋图标 - 实景任务 */',
    'point': '       /* 点位图标 */',
    'clipboard': '   /* 剪贴板 - 派单图标 */',
    'camera': '      /* 相机拍照图标 */',
    'album': '       /* 相册图标 */',
    'settings': '    /* 设置图标 */',
    'refresh': '     /* 刷新图标 */',
    'exchange': '    /* 交换图标 */',
    'calendar': '    /* 日历图标 */',
    'chart': '       /* 图表图标 */',
    'customers': '   /* 客户图标 */',
    'confirm': '     /* 确认图标 */',
    'cancel': '      /* 取消图标 */',
    'back': '        /* 返回按钮 */'
  };

  return comments[iconName] || '';
}

// 获取CSS尾部内容
function getCSSFooter() {
  return `
/* 导航图标样式 */
.icon-navigation {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: #007AFF;
  font-size: 16px;
  cursor: pointer;
}

.icon-navigation:active {
  opacity: 0.7;
}

/* 空状态图标 */
.empty-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  font-size: 48px;
  color: #C0C4CC;
}`;
}

// 主函数
function main() {
  const iconStatus = generateIconMapping();

  // 显示报告
  displayIconReport(iconStatus);

  // 生成建议
  console.log('\n💡 优化建议:');
  console.log('================================');

  const missingIcons = Object.keys(iconStatus).filter(icon =>
    iconStatus[icon].used && !iconStatus[icon].fontAvailable
  );

  const unusedIcons = Object.keys(iconStatus).filter(icon =>
    !iconStatus[icon].used && iconStatus[icon].defined
  );

  if (missingIcons.length > 0) {
    console.log('需要从iconfont下载的图标:');
    missingIcons.forEach(icon => {
      console.log(`   - ${icon}`);
    });
  }

  if (unusedIcons.length > 0) {
    console.log('可以删除的未使用图标:');
    unusedIcons.forEach(icon => {
      console.log(`   - ${icon}`);
    });
  }

  // 询问是否更新CSS文件
  console.log('\n是否要更新icons.css文件? (需要手动确认)');
  console.log('建议先备份当前文件，然后手动更新');
}

// 运行脚本
main();
