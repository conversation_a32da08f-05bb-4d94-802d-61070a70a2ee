<svg width="750" height="300" viewBox="0 0 750 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#007AFF;stop-opacity:0.8"/>
      <stop offset="100%" style="stop-color:#1E90FF;stop-opacity:0.9"/>
    </linearGradient>
  </defs>
  <rect width="750" height="300" fill="url(#headerGradient)" rx="15"/>
  <text x="375" y="150" font-family="Arial, sans-serif" font-size="48" fill="white" text-anchor="middle" font-weight="bold">群忠派单系统</text>
  <path d="M280,180 Q375,220 470,180" stroke="white" stroke-width="3" fill="none" opacity="0.6"/>
  <circle cx="280" cy="180" r="4" fill="white" opacity="0.8"/>
  <circle cx="470" cy="180" r="4" fill="white" opacity="0.8"/>
</svg>