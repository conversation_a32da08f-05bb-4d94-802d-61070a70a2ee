import { computed, ref, readonly } from 'vue';
import { useTaskStore } from '@/stores/task';
import { useImagesStore } from '@/stores/images';

/**
 * 优化的任务列表组合式函数
 * 提供高性能的任务数据访问和操作
 */
export const useOptimizedTaskList = () => {
	const taskStore = useTaskStore();
	const imagesStore = useImagesStore();

	// 分页状态
	const currentPage = ref(0);
	const pageSize = ref(50);

	// 获取优化的任务核心数据
	const taskCores = computed(() => taskStore.taskCores);

	// 获取统计信息
	const stats = computed(() => taskStore.stats);

	// 筛选后的任务核心数据
	const filteredTaskCores = computed(() => {
		let filtered = taskCores.value;
		const filters = taskStore.filters;

		// 搜索筛选
		if (filters.searchText) {
			const search = filters.searchText.toLowerCase();
			filtered = filtered.filter(task =>
				task.code.toLowerCase().includes(search) ||
				task.district.toLowerCase().includes(search)
			);
		}

		// 任务类型筛选
		if (filters.taskType && filters.taskType !== 'all') {
			filtered = filtered.filter(task => task.type === filters.taskType);
		}

		// 完成状态筛选
		if (filters.completionStatus) {
			if (filters.completionStatus === 'completed') {
				filtered = filtered.filter(task => task.completed);
			} else if (filters.completionStatus === 'incomplete') {
				filtered = filtered.filter(task => !task.completed);
			}
		}

		// 拍照状态筛选
		if (filters.photoStatus) {
			filtered = filtered.filter(task => {
				const hasImages = task.imageCount > 0;
				const isComplete = task.imageCount >= task.photoMax;

				switch (filters.photoStatus) {
					case 'not_taken':
						return task.imageCount === 0;
					case 'taken':
						return hasImages && !isComplete;
					case 'complete':
						return isComplete;
					default:
						return true;
				}
			});
		}

		return filtered;
	});

	// 分页后的任务数据
	const paginatedTaskCores = computed(() => {
		const start = 0;
		const end = (currentPage.value + 1) * pageSize.value;
		return filteredTaskCores.value.slice(start, end);
	});

	// 是否还有更多数据
	const hasMore = computed(() => {
		return (currentPage.value + 1) * pageSize.value < filteredTaskCores.value.length;
	});

	// 加载更多数据
	const loadMore = () => {
		if (hasMore.value) {
			currentPage.value++;
		}
	};

	// 重置分页
	const resetPagination = () => {
		currentPage.value = 0;
	};

	// 获取完整任务数据（按需）
	const getFullTask = (taskId: string) => {
		return taskStore.getFullTask(taskId);
	};

	// 获取任务统计信息（高性能）
	const getTaskStats = (taskId: string, photoMax: number) => {
		return imagesStore.getTaskStats(taskId, photoMax);
	};

	// 获取任务显示数据（用于UI渲染）
	const getTaskDisplayData = (taskCore: any) => {
		const fullTask = getFullTask(taskCore.id);
		const taskStats = getTaskStats(taskCore.id, taskCore.photoMax);

		return {
			// 核心数据（轻量）
			taskId: taskCore.id,
			deliveryId: taskCore.deliveryId,
			type: taskCore.type,
			spotId: taskCore.type === 'spot' ? taskCore.id : null,
			spotCode: taskCore.type === 'spot' ? taskCore.code : null,
			zoneName: taskCore.type === 'zone' ? taskCore.code : fullTask?.zoneName,
			belongDistrict: taskCore.district,
			photoMax: taskCore.photoMax,
			completed: taskCore.completed,
			imageCount: taskCore.imageCount,

			// 统计数据
			stats: taskStats,

			// 完整数据（按需）
			fullData: fullTask,

			// 显示用的计算属性
			displayName: taskCore.type === 'spot' ? taskCore.code : taskCore.district,
			statusText: taskCore.completed ? '已完成' : '进行中',
			progressText: `${taskStats.hasTaken + taskStats.pendingUpload}/${taskCore.photoMax}`,
			isComplete: (taskStats.hasTaken + taskStats.pendingUpload) >= taskCore.photoMax,

			// 图片URL
			currentContentImageUrl: fullTask?.currentContentImageUrl || '',
			previousContentImageUrl: fullTask?.previousContentImageUrl || ''
		};
	};

	// 批量获取显示数据
	const getDisplayDataList = computed(() => {
		return paginatedTaskCores.value.map(taskCore => getTaskDisplayData(taskCore));
	});

	// 更新任务状态（同时更新优化层）
	const updateTaskStatus = async (taskId: string, newStatus: string) => {
		// 更新兼容层
		const success = taskStore.updateTaskStatus(taskId, newStatus);

		if (success) {
			// 更新优化层
			const taskCore = taskCores.value.find(t => t.id === taskId);
			if (taskCore) {
				taskCore.completed = newStatus === 'COMPLETED';
				taskCore.lastUpdate = Date.now();
			}
		}

		return success;
	};

	// 更新任务图片（同时更新优化层）
	const updateTaskImages = async (taskId: string, newImage: any) => {
		// 更新兼容层
		const success = taskStore.updateTaskImages(taskId, newImage);

		if (success) {
			// 更新优化层
			const taskCore = taskCores.value.find(t => t.id === taskId);
			if (taskCore) {
				const taskStats = getTaskStats(taskId, taskCore.photoMax);
				taskCore.imageCount = taskStats.hasTaken + taskStats.pendingUpload;
				taskCore.lastUpdate = Date.now();
			}
		}

		return success;
	};

	return {
		// 数据
		taskCores: readonly(taskCores),
		filteredTaskCores: readonly(filteredTaskCores),
		paginatedTaskCores: readonly(paginatedTaskCores),
		getDisplayDataList,
		stats: readonly(stats),

		// 分页
		currentPage: readonly(currentPage),
		hasMore,
		loadMore,
		resetPagination,

		// 操作
		getFullTask,
		getTaskStats,
		getTaskDisplayData,
		updateTaskStatus,
		updateTaskImages,

		// 工具
		pageSize: readonly(pageSize)
	};
};
