# 数据管理架构迁移 - 统一到store/data.ts

## 🎯 **迁移目标**

将数据管理（clientStore/xxxState）统一迁移到`store/data.ts`中，让delivery/images/task保持相对独立的纯粹的页面响应操作。

## 🏗️ **新的架构设计**

### 1. **数据层分离**
```
┌─────────────────────────────────────────────────────────────┐
│                    store/data.ts                            │
│                   (统一数据管理)                              │
├─────────────────────────────────────────────────────────────┤
│ • clientStore (deliveryMap, taskMap, imageMap, uploadMap)  │
│ • deliveryStates, taskStates, uploadState                  │
│ • 数据加载、缓存、状态管理                                     │
│ • 图片管理、任务状态更新                                       │
└─────────────────────────────────────────────────────────────┘
                              ↑
                    统一数据接口调用
                              ↓
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  store/delivery │  │   store/task    │  │  store/images   │
│   (页面响应)     │  │   (页面响应)     │  │   (页面响应)     │
├─────────────────┤  ├─────────────────┤  ├─────────────────┤
│ • 派单筛选      │  │ • 任务筛选      │  │ • 图片显示      │
│ • 派单合并      │  │ • 任务排序      │  │ • 上传管理      │
│ • 页面状态      │  │ • 页面状态      │  │ • 页面状态      │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

### 2. **职责分离**
- **store/data.ts**: 统一的数据管理中心
- **store/delivery.ts**: 纯粹的派单页面响应操作
- **store/task.ts**: 纯粹的任务页面响应操作  
- **store/images.ts**: 纯粹的图片页面响应操作

## 📁 **已创建的store/data.ts**

### ✅ **核心数据存储**
```typescript
const clientStore = ref<ClientStore>({
    deliveryMap: new Map<string, Delivery>(),
    taskMap: new Map<string, Task>(),
    imageMap: new Map<string, TaskImages>(),
    uploadMap: new Map<string, TaskImages>(),
});

const deliveryStates = ref<Map<string, DeliveryState>>(new Map());
const taskStates = ref<Map<string, TaskState>>(new Map());
const uploadState = ref<UploadState>({...});
```

### ✅ **响应式计算属性**
```typescript
const getDeliveries = computed(() => {...});
const getTasks = computed(() => {...});
const getServerImages = computed(() => {...});
const getLocalImages = computed(() => {...});
const getDeliveryState = computed(() => (deliveryId: string) => {...});
const getTaskState = computed(() => (taskId: string) => {...});
```

### ✅ **数据加载方法**
```typescript
const loadData = async (forceLoad = false) => {...};
const refreshData = async () => {...};
const saveToCache = () => {...};
const loadFromCache = () => {...};
```

### ✅ **图片管理方法**
```typescript
const getTaskServerImages = (taskId: string | number) => {...};
const getTaskLocalImages = (taskId: string | number) => {...};
const getTaskAllImages = (taskId: string | number) => {...};
const addServerImage = (image: TaskImages) => {...};
const addLocalImage = (image: TaskImages) => {...};
const removeLocalImage = (imageId: string) => {...};
const updateImageStatus = (imageId: string, status: string) => {...};
const getTaskImageStats = (taskId: string, photoMax: number) => {...};
const cleanupUploadedImages = () => {...};
const moveImageToServer = (imageId: string) => {...};
```

### ✅ **状态管理方法**
```typescript
const initDeliveryStates = () => {...};
const initTaskStates = () => {...};
const initUploadState = () => {...};
const updateTaskStatus = (taskId: string, newStatus: string) => {...};
const updateTaskStateForImage = (taskId: string) => {...};
```

## 🔄 **需要进行的迁移步骤**

### 1. **更新store/delivery.ts**
- ✅ 移除clientStore相关代码
- ✅ 移除数据加载逻辑
- ✅ 移除图片管理方法
- ⏳ 更新为调用dataStore的方法
- ⏳ 保留派单筛选、合并等页面响应逻辑

### 2. **更新store/task.ts**
- ✅ 移除数据获取逻辑
- ⏳ 更新为调用dataStore的方法
- ⏳ 保留任务筛选、排序等页面响应逻辑

### 3. **更新store/images.ts**
- ⏳ 移除本地图片存储逻辑
- ⏳ 更新为调用dataStore的方法
- ⏳ 保留图片显示、上传管理等页面响应逻辑

### 4. **更新页面调用**
- ✅ 更新task/index.vue使用dataStore
- ⏳ 更新delivery/index.vue使用dataStore
- ⏳ 更新images/index.vue使用dataStore
- ⏳ 更新task-detail.vue使用dataStore

## 🎯 **迁移后的优势**

### ✅ **架构清晰**
- **单一数据源**: 所有数据统一在dataStore中管理
- **职责分离**: 各store专注于自己的页面响应逻辑
- **依赖清晰**: 页面store只依赖dataStore，不相互依赖

### ✅ **维护性提升**
- **代码复用**: 数据操作方法可以被多个store复用
- **调试简化**: 数据问题只需要在dataStore中调试
- **扩展容易**: 新增数据类型只需要在dataStore中添加

### ✅ **性能优化**
- **统一缓存**: 避免重复的数据加载和缓存
- **响应式优化**: 统一的dataVersion管理响应式更新
- **内存优化**: 避免数据在多个store中重复存储

### ✅ **类型安全**
- **统一接口**: 所有数据操作都有明确的类型定义
- **错误减少**: 减少因数据结构不一致导致的错误
- **IDE支持**: 更好的代码提示和错误检查

## 📝 **下一步计划**

### 1. **立即执行**
- 更新delivery.ts调用dataStore方法
- 更新task.ts调用dataStore方法
- 更新images.ts调用dataStore方法

### 2. **页面更新**
- 更新所有页面组件使用新的store结构
- 测试数据流的正确性
- 验证响应式更新的效果

### 3. **优化完善**
- 添加错误处理和边界情况处理
- 优化性能和内存使用
- 完善文档和注释

## 🧪 **验证方法**

### 1. **功能验证**
- 数据加载和显示正常
- 图片上传和管理正常
- 任务状态更新正常
- 页面跳转和状态保持正常

### 2. **性能验证**
- 页面加载速度
- 内存使用情况
- 响应式更新效率
- 缓存命中率

### 3. **架构验证**
- 代码结构清晰
- 依赖关系简单
- 扩展性良好
- 维护性提升

这个架构迁移将显著提升代码的可维护性和扩展性，为后续的功能开发奠定坚实的基础。
