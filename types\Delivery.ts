import { Task } from './Task';

/** OMaps —— Delivery, 派单信息 */
export interface Delivery {
	/** 主键 */
	deliveryId?: string;
	deliveryType?: 'up' | 'down' | 'monitor';
	/** 派单名称 */
	deliveryName?: string;
	/** 派单时间 */
	deliveryDate?: Date;
	/** 实景拍照要求，可不填写，最多可输入100个字符 */
	photoRequirements?: string;

	/** 上画图片 */
	currentContentImageUrl?: string;

	totalCount: 0,
	completeCount: 0,
	uncompleteCount: 0,
	spotTaskCount: 0,
	spotTaskComplete: 0,
	spotTaskUncomplete: 0,
	zoneTaskCount: 0,
	zoneTaskComplete: 0,
	zoneTaskUncomplete: 0
}
