<template>
  <view class="images-navbar">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- 主导航栏 -->
    <view class="navbar-content">
      <!-- 左侧总数和网络状态 -->
      <view class="navbar-left">
        <text class="total-count">总数: {{ totalCount }}</text>
      </view>

      <!-- 中间标题和搜索 -->
                     <!-- 中间标题和搜索 -->
      <view v-if="isUploading && !isPaused && total > 0" class="navbar-center">
        <view  class="title-container">
          <text class="progress-text">
            队列：{{ current + 1 }}/{{ total }}
          </text>
        </view>
      </view>

      <view v-else class="navbar-center">
        <view v-if="!showSearchInput" class="title-container">
          <text class="title">{{ title }}</text>
        </view>
        <view v-if="!showSearchInput" class="search-icon" @click="toggleSearchInput">
          <text class="icon iconfont icon-search"></text>
        </view>

        <!-- 搜索输入框 -->
        <view v-if="showSearchInput" class="search-input-container">
          <input
            class="search-input"
            type="text"
            v-model="searchText"
            placeholder="搜索"
            confirm-type="search"
            @confirm="handleSearch"
            focus
          />
          <view class="search-actions">
            <view class="search-button" @click="handleSearch">
              <text class="icon iconfont icon-search"></text>
            </view>
            <view class="close-button" @click="toggleSearchInput">
              <text class="icon iconfont icon-close"></text>
            </view>
          </view>
        </view>
      </view>

      <!-- 右侧上传按钮 -->
      <view class="navbar-right">
        <view class="upload-btn" @click="onUploadClick"
          :class="{
            'disabled': pendingCount === 0 && !isUploading,
            'uploading': isUploading && !isPaused,
            'paused': isUploading && isPaused,
            'wifi': isWifi
          }">
          <text class="network-icon iconfont" :class="isWifi ? 'icon-wifi' : 'icon-exchange'"></text>
          <text class="btn-text">{{ uploadButtonText }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';

// 声明全局 uni 对象，解决 TypeScript 错误
declare const uni: any;

// 定义组件属性
const props = defineProps({
  title: {
    type: String,
    default: '图片上传'
  },
  totalCount: {
    type: [Number, String],
    default: 0
  },
  pendingCount: {
    type: [Number, String],
    default: 0
  },
  // 上传状态
  isUploading: {
    type: Boolean,
    default: false
  },
  // 是否暂停
  isPaused: {
    type: Boolean,
    default: false
  },
  // 是否WiFi连接
  isWifi: {
    type: Boolean,
    default: false
  },
  // 网络类型
  networkType: {
    type: String,
    default: 'unknown'
  },
  // 当前上传索引
  current: {
    type: Number,
    default: 0
  },
  // 总上传数量
  total: {
    type: Number,
    default: 0
  }
});

// 上传按钮文本
const uploadButtonText = computed(() => {
  if (props.isUploading) {
    return props.isPaused ? '继续' : '暂停';
  }
  return '上传';
});

// 定义事件
const emit = defineEmits(['search', 'menu', 'dropdown', 'upload']);

// 状态栏高度
const statusBarHeight = ref(20);

// 从本地存储加载搜索框状态
const loadSearchState = () => {
  try {
    const showSearch = uni.getStorageSync('images_navbar_show_search');
    const searchValue = uni.getStorageSync('images_navbar_search_text');
    return {
      showSearch: showSearch === 'true',
      searchText: searchValue || ''
    };
  } catch (error) {
    console.warn('加载搜索框状态失败:', error);
    return { showSearch: false, searchText: '' };
  }
};

// 保存搜索框状态到本地存储
const saveSearchState = (show: boolean, text: string) => {
  try {
    uni.setStorageSync('images_navbar_show_search', show.toString());
    uni.setStorageSync('images_navbar_search_text', text);
  } catch (error) {
    console.warn('保存搜索框状态失败:', error);
  }
};

// 加载搜索框状态
const searchState = loadSearchState();

// 是否显示搜索输入框
const showSearchInput = ref(searchState.showSearch);
// 搜索文本
const searchText = ref(searchState.searchText);

// 获取状态栏高度
onMounted(() => {
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
});

// 切换搜索输入框显示状态
const toggleSearchInput = () => {
  showSearchInput.value = !showSearchInput.value;
  if (!showSearchInput.value) {
    // 如果隐藏搜索框，发送当前搜索内容
    emit('search', searchText.value);
  }
  // 保存搜索框状态
  saveSearchState(showSearchInput.value, searchText.value);
};

// 处理搜索
const handleSearch = () => {
  emit('search', searchText.value);
  // 保存搜索框状态
  saveSearchState(showSearchInput.value, searchText.value);
};

// 处理刷新点击
const onMenuClick = () => {
  emit('menu');
};

// 处理上传按钮点击
const onUploadClick = () => {
  if (props.pendingCount > 0 || props.isUploading) {
    emit('upload');
  }
};

</script>

<style lang="scss" scoped>
.images-navbar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;

  .status-bar {
    background-color: #6CA5F2; // 深绿色状态栏
  }

  .navbar-content {
    height: 44px;
    background-color: #6CA5F2; // 蓝色导航栏
    display: flex;
    align-items: center;
    padding: 0 15px;

    .navbar-left {
      flex: 1;
      display: flex;
      align-items: center;

      .total-count {
        color: #FFFFFF;
        font-size: 18px;
        font-weight: 600;
        margin-right: 10px;
      }

    }

    .navbar-center {
      flex: 2;
      display: flex;
      justify-content: center;
      align-items: center;

      .title-container {
        display: flex;
        align-items: center;
        margin-right: 10px;

        .title {
          color: #FFFFFF;
          font-size: 20px;
          font-weight: 700;
          margin-right: 5px;
        }

        .progress-text {
          color: #FFFFFF;
          font-size: 18px;
          font-weight: 600;
        }

        .dropdown-icon {
          color: #FFFFFF;
          font-size: 16px;
        }
      }

      .search-icon {
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;

        .icon {
          font-size: 16px;
          color: #FFFFFF;
          transform: scale(1.1);
        }
      }

      .search-input-container {
        display: flex;
        flex: 1;
        height: 36px;
        background-color: transparent;
        padding: 0;
        align-items: center;
        justify-content: center;
        width: 100%;

        .search-input {
          flex: 1;
          height: 36px;
          color: #FFFFFF;
          background-color: transparent;
          border: none;
          border-bottom: 1px solid rgba(255, 255, 255, 0.7);
          padding: 0 5px;
        }

        .search-actions {
          display: flex;
          align-items: center;

          .search-button, .close-button {
            width: 36px;
            height: 36px;
            display: flex;
            justify-content: center;
            align-items: center;

            .icon {
              color: #FFFFFF;
              font-size: 16px;
              transform: scale(1.1);
            }
          }
        }
      }
    }

    .navbar-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;

      .upload-btn {
        padding: 6px 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        min-width: 60px;

        .network-icon {
          font-size: 14px;
          margin-right: 4px;
          color: #FFFFFF;
        }

        .btn-text {
          font-size: 12px;
          color: #FFFFFF;
          font-weight: 600;
          letter-spacing: 0.3px;
        }

        &:active {
          transform: translateY(1px);
          box-shadow: 0 1px 4px rgba(102, 126, 234, 0.4);
        }

        &.disabled {
          background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
          box-shadow: 0 2px 8px rgba(149, 165, 166, 0.2);

          .network-icon {
            opacity: 0.7;
          }

          .btn-text {
            color: rgba(255, 255, 255, 0.8);
          }
        }

        &.uploading {
          background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
          box-shadow: 0 2px 8px rgba(86, 171, 47, 0.4);
          animation: pulse 2s infinite;

          .network-icon {
            color: #FFFFFF;
          }

          .btn-text {
            color: #FFFFFF;
          }
        }

        &.paused {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);

          .network-icon {
            color: #FFFFFF;
          }

          .btn-text {
            color: #FFFFFF;
          }
        }

        &.wifi {
          .network-icon {
            color: #4ECDC4;
          }

          &.uploading .network-icon,
          &.paused .network-icon {
            color: #FFFFFF;
          }

          &:not(.uploading):not(.paused) {
            box-shadow: 0 2px 8px rgba(78, 205, 196, 0.3);
          }
        }
      }

      @keyframes pulse {
        0% {
          box-shadow: 0 2px 8px rgba(86, 171, 47, 0.4);
        }
        50% {
          box-shadow: 0 4px 16px rgba(86, 171, 47, 0.6);
        }
        100% {
          box-shadow: 0 2px 8px rgba(86, 171, 47, 0.4);
        }
      }
    }
  }
}
</style>
