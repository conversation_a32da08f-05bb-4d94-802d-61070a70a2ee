<template>
	<view class="my-container" :style="dynamicFontStyle">
		<!-- 用户信息区 -->
		<view class="content-wrapper">
			<view class="user-info">
				<view class="avatar">
					<image :src="authStore.getUserAvatar()" mode="aspectFill"></image>
				</view>
				<view class="info">
					<text class="username"
						>{{ authStore.getUsername() || '未登录' }}「{{ authStore.getNickname() }}」</text
					>
				</view>
			</view>

			<!-- 功能区域 -->
			<!-- 
			<view class="feature-section">
				<view class="feature-grid">
					<view v-for="(item, index) in featureMenuItems" :key="index" class="feature-item"
						@click="handleFeatureClick(item)">
						<view class="feature-icon">
							<text class="iconfont" :class="getIconClass(item.id)"></text>
						</view>
						<text class="feature-text">{{ item.title }}</text>
					</view>
				</view>
			</view>
			-->
		</view>

		<!-- 用户设置区 -->
		<view class="settings-wrapper">
			<view class="settings-section">
				<view class="section-title">用户设置</view>

				<!-- 字体大小设置 -->
				<view class="setting-item">
					<text class="setting-label">字体大小</text>
					<view class="setting-options radio-options horizontal">
						<radio-group @change="(e) => updateSetting('fontSize', e.detail.value)">
							<label v-for="(option, index) in fontSizeOptions" :key="index" class="radio-label">
								<radio
									:value="option.value"
									:checked="userSettings.fontSize === option.value"
									color="#6CA5F2" />
								<text>{{ option.label }}</text>
							</label>
						</radio-group>
					</view>
				</view>

				<!-- 任务打开模式设置 -->
				<view class="setting-item">
					<text class="setting-label">任务打开模式</text>
					<view class="setting-options radio-options vertical">
						<radio-group @change="(e) => updateSetting('taskMode', e.detail.value)">
							<label v-for="(option, index) in taskModeOptions" :key="index" class="radio-label">
								<radio
									:value="option.value"
									:checked="userSettings.taskMode === option.value"
									color="#6CA5F2" />
								<text>{{ option.label }}</text>
							</label>
						</radio-group>
					</view>
				</view>

				<!-- 图片显示模式设置 -->
				<view class="setting-item">
					<text class="setting-label">图片显示模式</text>
					<view class="setting-options radio-options vertical">
						<radio-group @change="(e) => updateSetting('imageDisplayMode', e.detail.value)">
							<label v-for="(option, index) in imageDisplayModeOptions" :key="index" class="radio-label">
								<radio
									:value="option.value"
									:checked="userSettings.imageDisplayMode === option.value"
									color="#6CA5F2" />
								<text>{{ option.label }}</text>
							</label>
						</radio-group>
					</view>
				</view>

				<!-- 上传网络设置 -->
				<view class="setting-item wifi-setting">
					<text class="setting-label">仅WIFI时上传图片</text>
					<view class="switch-container">
						<switch
							:checked="userSettings.wifiOnly"
							@change="(e) => updateSetting('wifiOnly', e.detail.value)"
							color="#6CA5F2"
							style="margin-right: 0" />
					</view>
				</view>

				<!-- 已完成待上传任务置底设置 -->
				<!--
				<view class="setting-item wifi-setting">
					<text class="setting-label">已完成待上传任务置底</text>
					<view class="switch-container">
						<switch :checked="userSettings.completedTasksBottom"
							@change="(e) => updateSetting('completedTasksBottom', e.detail.value)" color="#0CBFB0"
							style="margin-right: 0;" />
					</view>
				</view>
				-->

				<!-- 版本信息显示 -->
				<view class="setting-item version-info">
					<text class="setting-label">版本信息</text>
					<text class="version-text">{{ versionInfo }}</text>
				</view>
			</view>

			<view class="logout-button-container">
				<button class="logout-button" @click="handleLogout">退出登录</button>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import { showToast } from '@/utils';
import { useSettingsStore, useAuthStore } from '@/stores';
import { VERSION } from '@/utils/env';

// 声明全局 uni 对象，解决 TypeScript 错误
declare const uni: any;

// 状态栏高度
const statusBarHeight = ref(20);

// 获取状态栏高度
onMounted(() => {
	const sysInfo = uni.getSystemInfoSync();
	statusBarHeight.value = sysInfo.statusBarHeight;
});

// 功能菜单项
const featureMenuItems = ref([
	{ id: 'history', title: '历史派单', icon: 'icon-history' },
	{ id: 'attendance', title: '我的出勤', icon: 'icon-attendance' },
	{ id: 'customer', title: '客户跟进', icon: 'icon-customer' },
	{ id: 'more', title: '其他功能', icon: 'icon-more' },
]);

// 字体大小选项
const fontSizeOptions = [
	{ label: '标准', value: 'normal' },
	{ label: '较大', value: 'large' },
	{ label: '超大', value: 'xlarge' },
];

// 任务打开模式选项
const taskModeOptions = [
	{ label: '普通', value: 'normal' },
	{ label: '极速', value: 'quick' },
];

// 图片显示模式选项
const imageDisplayModeOptions = [
	{ label: '智能模式（WiFi标准，否则省流）', value: 'smart' },
	{ label: '省流模式（总是显示占位符）', value: 'dataSaver' },
	{ label: '标准模式（总是显示图片）', value: 'standard' },
];

// 初始化 store
const settingsStore = useSettingsStore();
const authStore = useAuthStore();
// 从 store 中获取设置
const userSettings = settingsStore.settings;

// 字体大小管理
const currentFontScale = ref(1);

// 动态字体样式
const dynamicFontStyle = computed(() => ({
	'--font-scale': currentFontScale.value,
}));

// 初始化字体大小
const initFontSize = () => {
	try {
		const savedFontScale = uni.getStorageSync('fontScale') || 1;
		currentFontScale.value = savedFontScale;
		console.info(`📄 [MY] 页面字体大小初始化: 缩放 ${savedFontScale}`);
	} catch (error) {
		console.error('页面字体大小初始化失败:', error);
	}
};

// 处理字体大小变化
const handleFontSizeChange = (data: any) => {
	const { size, scale } = data;
	currentFontScale.value = scale;
	console.info(`📄 [MY] 页面字体大小已更新: ${size} (缩放: ${scale})`);
};

// 更新设置
const updateSetting = (key: string, value: any) => {
	if (key in userSettings) {
		settingsStore.updateSetting(key as any, value);
	}
};

// 版本信息计算属性
const versionInfo = computed(() => {
	return `${VERSION}`;
});

// 获取图标对应的iconfont类名
const getIconClass = (id: string): string => {
	const iconMap: Record<string, string> = {
		history: 'icon-calendar',
		attendance: 'icon-chart',
		customer: 'icon-customers',
		more: 'icon-more',
	};
	return iconMap[id] || 'icon-clipboard';
};

// 处理功能点击
const handleFeatureClick = (item: any) => {
	showToast(`${item.title}功能开发中`);
};

onMounted(() => {
	// 初始化字体大小
	initFontSize();

	// 监听字体大小变化
	uni.$on('fontSizeChanged', handleFontSizeChange);

	handlePageReady();
});

onShow(() => {
	handlePageReady();
});

onUnmounted(() => {
	// 清理事件监听
	uni.$off('fontSizeChanged', handleFontSizeChange);
});

const handlePageReady = async () => {
	if (!(await authStore.checkAuth())) {
		uni.reLaunch({
			url: '/pages/login/login',
		});
		return;
	}

	// 加载用户设置
	settingsStore.loadSettings();

	// 应用当前字体大小设置
	settingsStore.applyFontSize(userSettings.fontSize);
};

const handleLogout = () => {
	uni.showModal({
		title: '提示',
		content: '确定要退出登录吗？',
		confirmText: '确定',
		cancelText: '取消',
		success: (res: any) => {
			if (res.confirm) {
				authStore.logout();
			}
		},
	});
};
</script>

<style lang="scss" scoped>
.my-container {
	min-height: 100vh;
	background-color: $uni-bg-color-grey;

	// 应用字体缩放
	font-size: calc(16px * var(--font-scale, 1));

	.status-bar {
		background-color: #6ca5f2; // 蓝色导航栏
	}

	.navbar-content {
		height: 44px;
		background-color: #6ca5f2; // 蓝色导航栏
		display: flex;
		align-items: center;
		justify-content: center;

		.navbar-center {
			flex: 1;
			display: flex;
			justify-content: center;
			align-items: center;

			.title {
				color: #ffffff;
				font-size: 18px;
				font-weight: bold;
			}
		}
	}

	// .content-wrapper {
	// 	margin-top: calc(var(--status-bar-height)); // 导航栏高度 + 状态栏高度
	// }

	.user-info {
		padding: 1.5rem;
		background-color: $uni-bg-color;
		display: flex;
		align-items: center;
		margin-bottom: 0.625rem;
		box-shadow: 0 0.125rem 0.5rem $uni-bg-color-mask;

		.avatar {
			width: 4rem;
			height: 4rem;
			border-radius: 3rem;
			overflow: hidden;
			margin-right: 1.5rem;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.info {
			.username {
				font-size: 1rem;
				font-weight: bold;
				color: $uni-text-color;
			}
		}
	}

	.feature-section {
		background-color: $uni-bg-color;
		padding: 0.75rem 0;
		margin-bottom: 1px;
		width: 100%;

		.feature-grid {
			display: flex;
			flex-wrap: wrap;
			padding: 0 0.5rem;

			.feature-item {
				width: 25%;
				padding: 0.5rem;
				box-sizing: border-box;
				display: flex;
				flex-direction: column;
				align-items: center;
				transition: all 0.3s ease;

				.feature-icon {
					width: 4rem;
					height: 4rem;
					background-color: $uni-bg-color-grey;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-bottom: 0.5rem;

					.icon-emoji {
						font-size: 1rem;
					}
				}

				.feature-text {
					font-size: 1rem;
					color: $uni-text-color;
					text-align: center;
				}

				&:active {
					opacity: 0.7;
				}
			}
		}
	}

	.settings-wrapper {
		padding: 20rpx 0;
	}

	.settings-section {
		background-color: $uni-bg-color;
		padding: 1.5rem 1rem;
		margin-bottom: 20rpx;
		width: 100%;
		border-radius: 10rpx;
		box-sizing: border-box;

		.section-title {
			font-size: 1.2rem;
			font-weight: bold;
			color: $uni-text-color;
			margin-bottom: 1.5rem;
			padding-bottom: 0.625rem;
			border-bottom: 1px solid $uni-border-color;
		}

		.setting-item {
			padding: 30rpx 0;
			border-bottom: 1px solid #eee;
			margin-bottom: 20rpx;

			&:last-child {
				border-bottom: none;
				padding-bottom: 0;
			}

			&.wifi-setting {
				display: flex;
				justify-content: space-between;
				align-items: center;
				flex-wrap: nowrap;
				padding-right: 10px;
				/* 确保右侧有足够的空间 */
			}

			.setting-label {
				font-size: 1.125rem;
				color: $uni-text-color;
				font-weight: 500;
				margin-right: 10px;

				.wifi-setting & {
					margin-bottom: 0;
					display: inline-block;
					flex: 1;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}

			.setting-options {
				display: flex;
				flex-wrap: wrap;
				gap: 32rpx;
				margin-top: 26rpx !important;

				&.inline-options {
					flex-direction: row;
					align-items: center;
					justify-content: flex-start;
				}

				&.vertical-options {
					flex-direction: column;
					align-items: flex-start;
				}

				&.radio-options {
					margin-top: calc(var(--font-size-base) * 1);
					display: flex;
					flex-wrap: wrap;
					width: 100%;

					&.vertical {
						flex-direction: column;
						width: 100%;

						.radio-label {
							display: block;
							margin-bottom: calc(var(--font-size-base) * 1);
							width: 100%;

							&:last-child {
								margin-bottom: 0;
							}
						}
					}

					&.horizontal {
						flex-direction: row;
						align-items: center;
						justify-content: flex-start;
						gap: calc(var(--font-size-base) * 2.5);

						.radio-label {
							display: inline-flex;
							align-items: center;
							margin-bottom: 0;
							margin-right: 0;
							white-space: nowrap;
							padding: 0.5rem;

							&:last-child {
								margin-right: 0;
							}
						}
					}

					.radio-label {
						display: inline-flex;
						align-items: center;
						margin-right: calc(var(--font-size-base) * 1.25);
						padding: 15rpx 0;
						cursor: pointer;
						position: relative;
						z-index: 1;

						/* 增大点击区域 */
						&::before {
							content: '';
							position: absolute;
							top: -10rpx;
							left: -10rpx;
							right: -10rpx;
							bottom: -10rpx;
							z-index: -1;
						}

						/* 高亮选中效果 */
						&:active {
							opacity: 0.7;
						}

						radio {
							transform: scale(1.2);
						}

						text {
							margin-left: calc(var(--font-size-base) * 0.5);
							font-size: var(--font-size-base);
							color: $uni-text-color;
						}
					}
				}

				.option-item {
					padding: calc(var(--font-size-base) * 0.5) calc(var(--font-size-base) * 1);
					background-color: $uni-bg-color-grey;
					border-radius: calc(var(--font-size-base) * 0.375);
					font-size: var(--font-size-base);
					color: $uni-text-color;
					min-width: calc(var(--font-size-base) * 4);
					text-align: center;
					transition: all 0.3s ease;
					margin-right: 20rpx;
					margin-bottom: 10rpx;

					&.active {
						background-color: #6ca5f2; // 蓝色导航栏
						color: #fff;
					}

					&:active {
						opacity: 0.8;
					}
				}
			}

			.switch-container {
				min-width: 52px;
				/* 确保开关有足够的空间 */
				display: flex;
				justify-content: flex-end;
				margin-left: 10px;
			}

			switch {
				transform: scale(0.8);
				/* 稍微缩小开关 */
				vertical-align: middle;
			}

			.switch-label {
				font-size: var(--font-size-base);
				color: $uni-text-color-grey;
				vertical-align: middle;
			}

			&.version-info {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.version-text {
					font-size: 0.9rem;
					margin-right: 50rpx;
					color: $uni-text-color-grey;
				}
			}
		}
	}

	.logout-button-container {
		margin-top: 40rpx;
		padding: 0 20rpx;
		text-align: center;

		.logout-button {
			width: 100%;
			height: 3rem;
			line-height: 3rem;
			background-color: #6ca5f2; // 蓝色导航栏
			color: #ffffff;
			font-size: 1.125rem;
			border: none;
			border-radius: 10rpx;
			margin: 0 auto;
			transition: all 0.3s ease;

			&:active {
				opacity: 0.8;
			}
		}
	}
}
</style>
