// 事件总线 - 用于 store 之间的通信，避免循环依赖

interface EventCallback {
	(...args: any[]): void;
}

interface EventMap {
	[eventName: string]: EventCallback[];
}

class EventBus {
	private events: EventMap = {};

	// 监听事件
	on(eventName: string, callback: EventCallback) {
		if (!this.events[eventName]) {
			this.events[eventName] = [];
		}
		this.events[eventName].push(callback);
	}

	// 移除事件监听
	off(eventName: string, callback?: EventCallback) {
		if (!this.events[eventName]) return;

		if (callback) {
			const index = this.events[eventName].indexOf(callback);
			if (index > -1) {
				this.events[eventName].splice(index, 1);
			}
		} else {
			// 如果没有指定回调，移除所有监听器
			this.events[eventName] = [];
		}
	}

	// 触发事件
	emit(eventName: string, ...args: any[]) {
		if (!this.events[eventName]) return;

		this.events[eventName].forEach(callback => {
			try {
				callback(...args);
			} catch (error) {
				console.error(`[EventBus] 事件 ${eventName} 回调执行失败:`, error);
			}
		});
	}

	// 一次性监听事件
	once(eventName: string, callback: EventCallback) {
		const onceCallback = (...args: any[]) => {
			callback(...args);
			this.off(eventName, onceCallback);
		};
		this.on(eventName, onceCallback);
	}

	// 清除所有事件监听
	clear() {
		this.events = {};
	}

	// 获取事件监听器数量（调试用）
	getListenerCount(eventName: string): number {
		return this.events[eventName]?.length || 0;
	}

	// 获取所有事件名称（调试用）
	getEventNames(): string[] {
		return Object.keys(this.events);
	}
}

// 创建全局事件总线实例
export const eventBus = new EventBus();

// 定义事件名称常量
export const EVENTS = {
	// 任务相关事件
	TASKS_LOADED: 'tasks:loaded',
	TASKS_UPDATED: 'tasks:updated',
	TASK_STATUS_CHANGED: 'task:status_changed',
	
	// 派单相关事件
	DELIVERIES_LOADED: 'deliveries:loaded',
	DELIVERIES_UPDATED: 'deliveries:updated',
	DELIVERY_STATS_REQUEST: 'delivery:stats_request',
	DELIVERY_STATS_RESPONSE: 'delivery:stats_response',
	
	// 统计更新事件
	STATS_UPDATE_REQUEST: 'stats:update_request',
	STATS_UPDATED: 'stats:updated',
} as const;

// 类型定义
export interface DeliveryStatsRequest {
	deliveryId: string | string[];
	requestId: string;
}

export interface DeliveryStatsResponse {
	requestId: string;
	deliveryId: string | string[];
	stats: {
		requiredCount: number;
		uploadedCount: number;
		pendingCount: number;
		zoneTotalCount: number;
		spotTotalCount: number;
		zoneCompletedCount: number;
		spotCompletedCount: number;
		zoneUncompleteCount: number;
		spotUncompleteCount: number;
	};
}

export default eventBus;
