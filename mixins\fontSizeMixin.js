// 字体大小管理 mixin
export default {
  data() {
    return {
      currentFontScale: 1
    };
  },

  onLoad() {
    this.initPageFontSize();
    this.listenFontSizeChange();
  },

  onShow() {
    this.initPageFontSize();
  },

  onUnload() {
    // 移除字体大小变化监听
    uni.$off('fontSizeChanged', this.handleFontSizeChange);
  },

  methods: {
    // 初始化页面字体大小
    initPageFontSize() {
      try {
        const savedFontScale = uni.getStorageSync('fontScale') || 1;
        this.currentFontScale = savedFontScale;
        this.applyFontScaleToPage(savedFontScale);
        
        console.info(`📄 [PAGE] 页面字体大小初始化: 缩放 ${savedFontScale}`);
      } catch (error) {
        console.error('页面字体大小初始化失败:', error);
      }
    },

    // 监听字体大小变化
    listenFontSizeChange() {
      uni.$on('fontSizeChanged', this.handleFontSizeChange);
    },

    // 处理字体大小变化
    handleFontSizeChange(data) {
      const { size, scale } = data;
      this.currentFontScale = scale;
      this.applyFontScaleToPage(scale);
      
      console.info(`📄 [PAGE] 页面字体大小已更新: ${size} (缩放: ${scale})`);
    },

    // 应用字体缩放到页面
    applyFontScaleToPage(scale) {
      // 在移动端，我们通过设置页面的CSS变量来实现字体大小变化
      // #ifndef H5
      try {
        // 通过uni-app的方式设置页面样式
        // 注意：这里我们需要确保页面能够响应CSS变量的变化
        this.$nextTick(() => {
          // 强制页面重新渲染以应用新的字体大小
          this.$forceUpdate();
        });
      } catch (error) {
        console.error('应用字体缩放失败:', error);
      }
      // #endif

      // H5环境中，字体大小已经通过CSS类处理了
      // #ifdef H5
      // H5环境中不需要额外处理，CSS类已经生效
      // #endif
    }
  },

  // 计算属性：动态字体大小样式
  computed: {
    // 动态字体大小样式对象
    dynamicFontStyle() {
      return {
        '--font-scale': this.currentFontScale
      };
    },

    // 基础字体大小
    baseFontSize() {
      return `${16 * this.currentFontScale}px`;
    },

    // 小字体大小
    smallFontSize() {
      return `${14 * this.currentFontScale}px`;
    },

    // 大字体大小
    largeFontSize() {
      return `${18 * this.currentFontScale}px`;
    },

    // 超大字体大小
    xlargeFontSize() {
      return `${20 * this.currentFontScale}px`;
    }
  }
};
