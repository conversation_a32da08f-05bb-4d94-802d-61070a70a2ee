<template>
  <view class="smart-image-container" @click="handleClick">
    <!-- 省流模式：显示占位符 -->
    <view v-if="shouldShowPlaceholder" class="image-placeholder">
      <text class="placeholder-icon iconfont icon-album"></text>
      <text class="placeholder-text">{{ placeholderText }}</text>
      <text v-if="showDataSaverHint" class="placeholder-hint">(省流模式)</text>
    </view>

    <!-- 正常模式：显示图片 -->
    <image
      v-else
      :src="src"
      :mode="mode"
      :class="imageClass"
      :style="imageStyle"
      @load="handleLoad"
      @error="handleError"
      @click="handleImageClick"
    />

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-indicator">
      <text>加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view v-if="error" class="error-indicator">
      <text>加载失败</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useSettingsStore } from '@/stores/settings';
import { ImageDisplayUtils } from '@/utils/image-display';

// 声明全局 uni 对象
declare const uni: any;

// 定义组件属性
const props = defineProps({
  // 图片URL
  src: {
    type: String,
    required: true
  },
  // 图片模式
  mode: {
    type: String,
    default: 'aspectFill'
  },
  // 占位符文本
  placeholderText: {
    type: String,
    default: '点击查看图片'
  },
  // 图片CSS类
  imageClass: {
    type: String,
    default: ''
  },
  // 图片内联样式
  imageStyle: {
    type: [String, Object],
    default: ''
  },
  // 是否显示省流提示
  showDataSaverHint: {
    type: Boolean,
    default: true
  },
  // 是否可点击
  clickable: {
    type: Boolean,
    default: true
  }
});

// 定义事件
const emit = defineEmits(['click', 'load', 'error']);

// 状态管理
const loading = ref(false);
const error = ref(false);
const networkType = ref('unknown');
const hasCached = ref(false);
const checkingCache = ref(false);

// 获取设置store
const settingsStore = useSettingsStore();

// 检查图片缓存状态
const checkImageCache = async () => {
  if (!props.src) return;

  checkingCache.value = true;
  try {
    hasCached.value = await ImageDisplayUtils.checkImageCache(props.src);
    console.info('📷 [SMART-IMAGE] 缓存检查结果:', props.src, hasCached.value);
  } catch (error) {
    console.warn('📷 [SMART-IMAGE] 缓存检查失败:', error);
    hasCached.value = false;
  } finally {
    checkingCache.value = false;
  }
};

// 获取当前网络类型
const getCurrentNetworkType = () => {
  uni.getNetworkType({
    success: (res: any) => {
      networkType.value = res.networkType;
    },
    fail: () => {
      networkType.value = 'unknown';
    }
  });
};

// 计算是否应该显示占位符（优先考虑缓存）
const shouldShowPlaceholder = computed(() => {
  // 1. 如果图片已缓存，直接显示图片
  if (hasCached.value) {
    return false;
  }

  // 2. 如果没有缓存，根据设置和网络状态决定
  const imageMode = settingsStore.settings.imageDisplayMode;

  switch (imageMode) {
    case 'dataSaver':
      // 省流模式：显示占位符
      return true;
    case 'standard':
      // 标准模式：显示图片
      return false;
    case 'smart':
    default:
      // 智能模式：WiFi显示图片，移动网络显示占位符
      return networkType.value !== 'wifi';
  }
});

// 处理点击事件
const handleClick = (e: Event) => {
  if (!props.clickable) return;

  if (shouldShowPlaceholder.value) {
    // 如果是占位符，点击时预览图片
    previewImage();
  }
  // 如果是图片，不在这里处理点击事件，由 handleImageClick 处理
};

// 处理图片点击
const handleImageClick = (e: Event) => {
  e.stopPropagation();
  if (props.clickable) {
    emit('click', e);
  }
};

// 预览图片
const previewImage = () => {
  if (!props.src) {
    uni.showToast({
      title: '图片不存在',
      icon: 'none'
    });
    return;
  }

  uni.previewImage({
    urls: [props.src],
    current: props.src,
    fail: (error: any) => {
      console.error('预览图片失败:', error);
      uni.showToast({
        title: '预览图片失败',
        icon: 'none'
      });
    }
  });
};

// 处理图片加载完成
const handleLoad = (e: Event) => {
  loading.value = false;
  error.value = false;
  emit('load', e);
};

// 处理图片加载错误
const handleError = (e: Event) => {
  loading.value = false;
  error.value = true;
  emit('error', e);
};

// 监听图片URL变化，重新检查缓存
watch(() => props.src, () => {
  if (props.src) {
    checkImageCache();
  }
}, { immediate: true });

// 组件挂载时获取网络状态
onMounted(() => {
  getCurrentNetworkType();

  // 监听网络状态变化
  uni.onNetworkStatusChange?.((res: any) => {
    networkType.value = res.networkType;
  });

  // 初始检查缓存
  if (props.src) {
    checkImageCache();
  }
});
</script>

<style lang="scss" scoped>
.smart-image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1px dashed #ccc;
  border-radius: 8px;
  min-height: 120px;
  width: 100%;
  height: 100%;
  cursor: pointer;
  transition: all 0.2s ease;

  .placeholder-icon {
    font-size: 32px;
    color: #999;
    margin-bottom: 8px;
  }

  .placeholder-text {
    font-size: 14px;
    color: #666;
    text-align: center;
  }

  .placeholder-hint {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }

  &:active {
    background-color: #eeeeee;
    transform: scale(0.98);
  }
}

.loading-indicator,
.error-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10;
}

.error-indicator {
  background-color: rgba(220, 53, 69, 0.8);
}
</style>
