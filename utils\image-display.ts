import { ref, computed } from 'vue';
import { useSettingsStore } from '@/stores/settings';

// 声明全局 uni 对象
declare const uni: any;

// 响应式网络状态
const networkType = ref<string>('unknown');
const initialized = ref<boolean>(false);

/**
 * 图片显示工具类 - 支持Vue响应式
 */
export class ImageDisplayUtils {
  /**
   * 初始化网络状态监听
   */
  static initialize() {
    if (initialized.value) return;

    // 获取当前网络状态
    this.getCurrentNetworkType();

    // 监听网络状态变化
    if (typeof uni !== 'undefined') {
      uni.onNetworkStatusChange?.((res: any) => {
        networkType.value = res.networkType;
        console.info('📶 [IMAGE] 网络状态变化:', res.networkType);

        // 触发全局事件，通知组件更新
        uni.$emit('networkTypeChanged', {
          networkType: res.networkType,
          isWifi: res.networkType === 'wifi'
        });
      });
    }

    initialized.value = true;
    console.info('📶 [IMAGE] 图片显示工具初始化完成');
  }

  /**
   * 获取当前网络类型
   */
  private static getCurrentNetworkType(): Promise<string> {
    return new Promise((resolve) => {
      if (typeof uni === 'undefined') {
        resolve('unknown');
        return;
      }

      uni.getNetworkType({
        success: (res: any) => {
          this.networkType = res.networkType;
          resolve(res.networkType);
        },
        fail: () => {
          this.networkType = 'unknown';
          resolve('unknown');
        }
      });
    });
  }

  /**
   * 检查图片是否有本地缓存
   */
  static async checkImageCache(imageUrl: string): Promise<boolean> {
    if (!imageUrl || typeof uni === 'undefined') {
      return false;
    }

    try {
      // 检查图片是否已缓存到本地
      const result = await new Promise<boolean>((resolve) => {
        uni.getImageInfo({
          src: imageUrl,
          success: () => {
            // 图片信息获取成功，说明已缓存
            resolve(true);
          },
          fail: () => {
            // 图片信息获取失败，说明未缓存
            resolve(false);
          }
        });
      });

      return result;
    } catch (error) {
      console.warn('检查图片缓存失败:', error);
      return false;
    }
  }

  /**
   * 判断是否应该显示图片（优先考虑本地缓存）
   */
  static async shouldShowImage(imageUrl?: string): Promise<boolean> {
    // 1. 如果提供了图片URL，先检查本地缓存
    if (imageUrl) {
      const hasCached = await this.checkImageCache(imageUrl);
      if (hasCached) {
        console.info('📷 [IMAGE] 图片已缓存，直接显示:', imageUrl);
        return true;
      }
    }

    // 2. 如果没有缓存，根据用户设置和网络状态决定
    const settingsStore = useSettingsStore();
    const imageMode = settingsStore.settings.imageDisplayMode;

    switch (imageMode) {
      case 'dataSaver':
        // 省流模式：显示占位符
        return false;
      case 'standard':
        // 标准模式：显示图片
        return true;
      case 'smart':
      default:
        // 智能模式：WiFi显示图片，移动网络显示占位符
        return this.networkType === 'wifi';
    }
  }

  /**
   * 同步版本的判断方法（用于computed等场景）
   */
  static shouldShowImageSync(imageUrl?: string): boolean {
    const settingsStore = useSettingsStore();
    const imageMode = settingsStore.settings.imageDisplayMode;

    switch (imageMode) {
      case 'dataSaver':
        return false;
      case 'standard':
        return true;
      case 'smart':
      default:
        return this.networkType === 'wifi';
    }
  }

  /**
   * 判断是否应该显示占位符（异步版本）
   */
  static async shouldShowPlaceholder(imageUrl?: string): Promise<boolean> {
    return !(await this.shouldShowImage(imageUrl));
  }

  /**
   * 判断是否应该显示占位符（同步版本）
   */
  static shouldShowPlaceholderSync(imageUrl?: string): boolean {
    return !this.shouldShowImageSync(imageUrl);
  }

  /**
   * 获取当前网络类型
   */
  static getNetworkType(): string {
    return this.networkType;
  }

  /**
   * 判断是否为WiFi网络
   */
  static isWifi(): boolean {
    return this.networkType === 'wifi';
  }

  /**
   * 获取省流模式状态文本
   */
  static getDataSaverStatusText(): string {
    const settingsStore = useSettingsStore();
    const imageMode = settingsStore.settings.imageDisplayMode;

    switch (imageMode) {
      case 'dataSaver':
        return '省流模式';
      case 'standard':
        return '标准模式';
      case 'smart':
        return this.isWifi() ? '智能模式(WiFi)' : '智能模式(省流)';
      default:
        return '未知模式';
    }
  }

  /**
   * 预览图片
   */
  static previewImage(imageUrl: string, imageUrls?: string[]) {
    if (!imageUrl) {
      uni.showToast({
        title: '图片不存在',
        icon: 'none'
      });
      return;
    }

    const urls = imageUrls || [imageUrl];

    uni.previewImage({
      urls: urls,
      current: imageUrl,
      fail: (error: any) => {
        console.error('预览图片失败:', error);
        uni.showToast({
          title: '预览图片失败',
          icon: 'none'
        });
      }
    });
  }

  /**
   * 获取占位符文本
   */
  static getPlaceholderText(imageType?: string): string {
    const baseText = '点击查看';

    switch (imageType) {
      case 'history':
        return `${baseText}历史图片`;
      case 'current':
        return `${baseText}当前图片`;
      case 'local':
        return `${baseText}拍摄图片`;
      case 'server':
        return `${baseText}已上传图片`;
      default:
        return `${baseText}图片`;
    }
  }

  /**
   * 创建图片显示配置（异步版本，支持缓存检查）
   */
  static async createImageConfig(imageUrl: string, options: {
    imageType?: string;
    mode?: string;
    clickable?: boolean;
    showHint?: boolean;
  } = {}) {
    const shouldShow = await this.shouldShowImage(imageUrl);

    return {
      src: imageUrl,
      mode: options.mode || 'aspectFill',
      placeholderText: this.getPlaceholderText(options.imageType),
      clickable: options.clickable !== false,
      showDataSaverHint: options.showHint !== false,
      shouldShowImage: shouldShow,
      shouldShowPlaceholder: !shouldShow,
      hasCached: await this.checkImageCache(imageUrl)
    };
  }

  /**
   * 创建图片显示配置（同步版本）
   */
  static createImageConfigSync(imageUrl: string, options: {
    imageType?: string;
    mode?: string;
    clickable?: boolean;
    showHint?: boolean;
  } = {}) {
    const shouldShow = this.shouldShowImageSync(imageUrl);

    return {
      src: imageUrl,
      mode: options.mode || 'aspectFill',
      placeholderText: this.getPlaceholderText(options.imageType),
      clickable: options.clickable !== false,
      showDataSaverHint: options.showHint !== false,
      shouldShowImage: shouldShow,
      shouldShowPlaceholder: !shouldShow
    };
  }
}

// 自动初始化
ImageDisplayUtils.initialize();

// 导出便捷方法
export const shouldShowImage = (imageUrl?: string) => ImageDisplayUtils.shouldShowImage(imageUrl);
export const shouldShowImageSync = (imageUrl?: string) => ImageDisplayUtils.shouldShowImageSync(imageUrl);
export const shouldShowPlaceholder = (imageUrl?: string) => ImageDisplayUtils.shouldShowPlaceholder(imageUrl);
export const shouldShowPlaceholderSync = (imageUrl?: string) => ImageDisplayUtils.shouldShowPlaceholderSync(imageUrl);
export const checkImageCache = (imageUrl: string) => ImageDisplayUtils.checkImageCache(imageUrl);
export const previewImage = (url: string, urls?: string[]) => ImageDisplayUtils.previewImage(url, urls);
export const getPlaceholderText = (type?: string) => ImageDisplayUtils.getPlaceholderText(type);
export const createImageConfig = (imageUrl: string, options?: any) => ImageDisplayUtils.createImageConfig(imageUrl, options);
export const createImageConfigSync = (imageUrl: string, options?: any) => ImageDisplayUtils.createImageConfigSync(imageUrl, options);
