<template>
	<view class="delivery-container" :style="dynamicFontStyle">
		<!-- 派单导航栏 -->
		<delivery-navbar :total-count="getDeliveries.length" :current-count="filteredDeliveries.length"
			@search="onSearchClick" @refresh="onRefreshClick" @settings="onSettingsClick" />

		<!-- 筛选选项卡 -->
		<delivery-filter :initial-merge-type="filterSettings.mergeType" :initial-sort-order="filterSettings.sortOrder"
			@filter-change="handleFilterChange" />

		<!-- 内容区域 -->
		<scroll-view scroll-y refresher-enabled :refresher-triggered="refreshing" @refresherrefresh="onRefresh"
			refresher-threshold="150" class="delivery-list" :show-scrollbar="false">
			<view v-if="deliveries.length === 0" class="empty-state">
				<text>暂无派单信息</text>
				<text class="debug-info">调试信息: 原始数据={{ getDeliveries.length }}, 过滤后={{ filteredDeliveries.length }},
					版本={{ dataVersion }}</text>
			</view>
			<view v-else class="delivery-items"
				:class="{ 'has-merge-actions': isManualMergeMode && !manualMergedDelivery }">
				<view v-for="item in filteredDeliveries" :key="item.deliveryId" class="delivery-item"
					:class="{ 'checkbox-mode': isManualMergeMode && !manualMergedDelivery }"
					@click="handleDeliveryClick(item)">
					<!-- 手动选择合并模式下显示复选框 -->
					<checkbox v-if="isManualMergeMode && !manualMergedDelivery"
						:checked="selectedDeliveryIds.includes(item.deliveryId)" class="delivery-checkbox"
						@click.stop="handleDeliveryClick(item)" />

					<!-- 图片显示逻辑：
					     1. 在选择模式下不显示图片
					     2. 在常规模式下，如果是组合派单不显示图片，如果是非组合派单则显示图片并可点击放大
					-->
					<view class="delivery-left" v-if="!isManualMergeMode && !item.isGrouped">
						<smart-image v-if="
							item.currentContentImageUrl &&
							item.currentContentImageUrl !== '/static/default-image.svg'
						" :src="item.currentContentImageUrl" :placeholder-text="'查看派单图片'" :image-class="'task-image'"
							:image-style="'width: 100%; height: 100%;'" mode="aspectFit"
							@click="(event) => handleImageClick(item, event)" />
						<view v-else class="default-image-placeholder"
							@click.stop="(event) => handleImageClick(item, event)">
							<text class="default-image-text">空</text>
						</view>
					</view>
					<view class="delivery-right" :class="{ 'full-width': isManualMergeMode || item.isGrouped }">
						<view class="delivery-header">
							<view class="delivery-title">
								<text class="delivery-name" :data-badge="item.isGrouped ? '合并' : ''">
									{{ getDeliveryName(item.deliveryId) }}
									<!-- 显示合并的派单数量（新格式） -->
									<text v-if="item.isGrouped && item.groupItems" class="merged-count">
										等
										<text class="count-number">{{
											item.mergedCount || item.groupItems.length
										}}</text>
										个派单
									</text>
								</text>
							</view>
						</view>
						<view class="delivery-stats">
							<!-- 两栏布局：左边点位，右边实景 -->
							<view class="stats-columns">
								<!-- 左栏：点位统计 -->
								<view class="stats-column left-column">
									<view class="column-header">
										<text class="column-title">
											<text class="column-icon iconfont icon-point"></text>
											<text class="column-text">点位</text>
										</text>
									</view>
									<view class="column-stats">
										<text class="status-item highlight-stats-item compact"><text
												class="stats-label-highlight">总数量:</text>
											<text class="status-value stats-value-highlight total">{{
												getDeliveryStats(item).spotTotalCount }}</text></text>
										<text class="status-item highlight-stats-item compact"><text
												class="stats-label-highlight">已完成:</text>
											<text class="status-value stats-value-highlight completed">{{
												getDeliveryStats(item).spotCompletedCount }}</text></text>
										<text class="status-item highlight-stats-item compact" :class="{
											'zero-uncompleted': getDeliveryStats(item).spotUncompleteCount === 0
										}">
											<text class="stats-label-highlight" :class="{
												'zero-uncompleted-label': getDeliveryStats(item).spotUncompleteCount === 0
											}">未完成:</text>
											<text class="status-value stats-value-highlight warning-value uncompleted"
												:class="{
													'zero-uncompleted-value': getDeliveryStats(item).spotUncompleteCount === 0
												}">{{ getDeliveryStats(item).spotUncompleteCount }}</text></text>
									</view>
								</view>

								<!-- 右栏：实景统计 -->
								<view class="stats-column right-column">
									<view class="column-header">
										<text class="column-title">
											<text class="column-icon iconfont icon-house"></text>
											<text class="column-text">实景</text>
										</text>
									</view>
									<view class="column-stats">
										<text class="status-item highlight-stats-item compact"><text
												class="stats-label-highlight">总数量:</text>
											<text class="status-value stats-value-highlight total">{{
												getDeliveryStats(item).zoneTotalCount }}</text></text>
										<text class="status-item highlight-stats-item compact"><text
												class="stats-label-highlight">已完成:</text>
											<text class="status-value stats-value-highlight completed">{{
												getDeliveryStats(item).zoneCompletedCount }}</text></text>
										<text class="status-item highlight-stats-item compact" :class="{
											'zero-uncompleted': getDeliveryStats(item).zoneUncompleteCount === 0
										}">
											<text class="stats-label-highlight" :class="{
												'zero-uncompleted-label': getDeliveryStats(item).zoneUncompleteCount === 0
											}">未完成:</text>
											<text class="status-value stats-value-highlight warning-value uncompleted"
												:class="{
													'zero-uncompleted-value': getDeliveryStats(item).zoneUncompleteCount === 0
												}">{{ getDeliveryStats(item).zoneUncompleteCount }}</text></text>
									</view>
								</view>
							</view>
						</view>
						<view class="delivery-info">
							<text class="task-time">任务时间: {{ formatDate(item.deliveryDate) }}</text>
							<text v-if="item.queueId" class="queue-id">队列: {{ item.queueId }}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 手动选择合并模式下的底部操作栏（仅在选择阶段显示） -->
		<view v-if="isManualMergeMode && !manualMergedDelivery" class="merge-actions">
			<view class="action-button cancel" @click="cancelManualMerge">
				<text class="action-icon iconfont icon-cancel"></text>
				<text class="action-text">取消</text>
			</view>
			<view class="action-button confirm" @click="confirmManualMerge">
				<text class="action-icon iconfont icon-confirm"></text>
				<text class="action-text">确定</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useDeliveryStore } from '../../stores/delivery';
import { useTaskStore } from '../../stores/task';
import { useAuthStore } from '../../stores/auth';
import { useSettingsStore } from '../../stores/settings';
import { formatDate, showToast } from '../../utils';
import DeliveryNavbar from './components/delivery-navbar.vue';
import DeliveryFilter from './components/delivery-filter.vue';
import SmartImage from '../../components/smart-image.vue';

// 声明全局 uni 对象，解决 TypeScript 错误
declare const uni: any;

const deliveryStore = useDeliveryStore();
const taskStore = useTaskStore();
const { getDeliveries, refreshing, selectedDeliveryIds, manualMergedDelivery, filterSettings, dataVersion } = storeToRefs(deliveryStore);
const { loadDeliveries, refreshDeliveries, getDeliveryName, setSelectedDeliveryIds, setManualMergedDelivery, setFilterSettings, getFilteredDeliveries } = deliveryStore;
const { getDeliveryState } = taskStore;

// 搜索文本
const searchText = ref('');

// 获取派单统计数据（使用deliveryId调用）
const getDeliveryStats = (item: any) => {
	try {
		// 添加调试日志
		console.log(`📊 [DELIVERY_STATS] 获取统计数据 - deliveryId: ${item.deliveryId}, isGrouped: ${item.isGrouped}`);

		// 如果是合并的派单，需要合并所有子派单的统计数据
		if (item.isGrouped && item.groupItems) {
			console.log(`📊 [DELIVERY_STATS] 合并派单，子项数量: ${item.groupItems.length}`);
		return item.groupItems.reduce((total: any, groupItem: any) => {
			const stats = taskStore.getDeliveryState(groupItem.deliveryId);
			console.log(`📊 [DELIVERY_STATS] 子派单 ${groupItem.deliveryId} 统计:`, stats);
			return {
				spotTotalCount: total.spotTotalCount + (stats.spotTotalCount || 0),
				spotCompletedCount: total.spotCompletedCount + (stats.spotCompletedCount || 0),
				spotUncompleteCount: total.spotUncompleteCount + (stats.spotUncompleteCount || 0),
				zoneTotalCount: total.zoneTotalCount + (stats.zoneTotalCount || 0),
				zoneCompletedCount: total.zoneCompletedCount + (stats.zoneCompletedCount || 0),
				zoneUncompleteCount: total.zoneUncompleteCount + (stats.zoneUncompleteCount || 0)
			};
		}, {
			spotTotalCount: 0,
			spotCompletedCount: 0,
			spotUncompleteCount: 0,
			zoneTotalCount: 0,
			zoneCompletedCount: 0,
			zoneUncompleteCount: 0
		});
	}

	// 单个派单的统计数据 - 使用taskStore而不是deliveryStore
	const stats = taskStore.getDeliveryState(item.deliveryId);
	console.log(`📊 [DELIVERY_STATS] 单个派单 ${item.deliveryId} 统计:`, stats);

	const result = {
		spotTotalCount: stats.spotTotalCount || 0,
		spotCompletedCount: stats.spotCompletedCount || 0,
		spotUncompleteCount: stats.spotUncompleteCount || 0,
		zoneTotalCount: stats.zoneTotalCount || 0,
		zoneCompletedCount: stats.zoneCompletedCount || 0,
		zoneUncompleteCount: stats.zoneUncompleteCount || 0
	};

	console.log(`📊 [DELIVERY_STATS] 返回结果:`, result);
	return result;
	} catch (error) {
		console.error('❌ [DELIVERY_STATS] 获取统计数据失败:', error, 'item:', item);
		return {
			spotTotalCount: 0,
			spotCompletedCount: 0,
			spotUncompleteCount: 0,
			zoneTotalCount: 0,
			zoneCompletedCount: 0,
			zoneUncompleteCount: 0
		};
	}
};

// 下拉菜单是否显示
const dropdownVisible = ref(false);

// 字体大小管理
const currentFontScale = ref(1);

// 动态字体样式
const dynamicFontStyle = computed(() => ({
	'--font-scale': currentFontScale.value
}));

// 初始化字体大小
const initFontSize = () => {
	try {
		const savedFontScale = uni.getStorageSync('fontScale') || 1;
		currentFontScale.value = savedFontScale;
		console.info(`📄 [DELIVERY] 页面字体大小初始化: 缩放 ${savedFontScale}`);
	} catch (error) {
		console.error('页面字体大小初始化失败:', error);
	}
};

// 处理字体大小变化
const handleFontSizeChange = (data: any) => {
	const { size, scale } = data;
	currentFontScale.value = scale;
	console.info(`📄 [DELIVERY] 页面字体大小已更新: ${size} (缩放: ${scale})`);
};

// 监听任务完成事件
onMounted(() => {
	// 初始化字体大小
	initFontSize();

	// 监听字体大小变化
	uni.$on('fontSizeChanged', handleFontSizeChange);

	// 监听单个任务完成事件
	uni.$on('taskCompleted', (data: any) => {
		console.info('🎉 [TASK_COMPLETED] 收到任务完成事件:', data);
		// Vue的响应式系统会自动更新统计显示
	});

	// 监听批量任务完成事件
	uni.$on('batchTaskCompleted', (data: any) => {
		console.info('🎉 [BATCH_COMPLETED] 收到批量任务完成事件:', data);
		// Vue的响应式系统会自动更新统计显示
	});
});

onUnmounted(() => {
	// 清理事件监听
	uni.$off('fontSizeChanged', handleFontSizeChange);
	uni.$off('taskCompleted');
	uni.$off('batchTaskCompleted');
});

// 手动选择合并相关状态
const isManualMergeMode = ref(false); // 是否处于手动选择合并模式
const actualMergeType = ref(filterSettings.value.mergeType); // 实际的合并类型，初始值与当前合并类型一致

// 处理筛选变更
const handleFilterChange = (filterData: any) => {
	if (filterData.tabIndex === 0) {
		// 合并类型变更
		setFilterSettings({ mergeType: filterData.value });

		if (filterData.value === 'manual_merge') {
			// 当选择"手动合并"时

			// 如果已经有手动合并的结果，则直接显示手动合并的结果，不进入选择模式
			if (manualMergedDelivery.value) {
				// 设置实际类型为手动合并
				actualMergeType.value = 'manual_merge';
				// 不进入选择模式
				isManualMergeMode.value = false;
				// 清空选择
				setSelectedDeliveryIds([]);
			} else {
				// 如果没有手动合并的结果，则进入选择模式
				isManualMergeMode.value = true;
				// 清空选择
				setSelectedDeliveryIds([]);
				// 保持当前的合并类型，只是进入选择模式
				// 注意：这里不改变 actualMergeType，保持之前的合并类型
			}
		} else if (isManualMergeMode.value) {
			// 如果已经在选择模式下，并选择了其他合并类型
			// 只更新实际的合并类型，但保持在选择模式
			actualMergeType.value = filterData.value;

			// 注意：不退出选择模式，只有通过确认或取消按钮才能退出
		} else {
			// 常规模式下的处理
			// 如果有手动合并的派单，清除它（除非选择的是手动合并）
			if (manualMergedDelivery.value && filterData.value !== 'manual_merge') {
				setManualMergedDelivery(null);
			}

			// 确保不在选择模式
			isManualMergeMode.value = false;

			// 清空选中的派单ID
			setSelectedDeliveryIds([]);

			// 更新实际的合并类型
			actualMergeType.value = filterData.value;
		}
	} else if (filterData.tabIndex === 1) {
		// 排序方式变更
		setFilterSettings({ sortOrder: filterData.value });
	}
};

// 根据筛选条件过滤和排序派单列表
const deliveries = computed(() => getDeliveries.value);
const filteredDeliveries = computed(() => {
	// 使用 store 中的 getFilteredDeliveries 方法，传入搜索文本和 taskStore
	let result = getFilteredDeliveries(searchText.value, taskStore);

	// 处理手动合并模式
	if (isManualMergeMode.value) {
		// 如果在选择模式下已经有手动合并的结果，并且实际类型是手动合并，则显示手动合并的结果
		if (manualMergedDelivery.value && actualMergeType.value === 'manual_merge') {
			return [manualMergedDelivery.value];
		}

		// 根据实际选择的合并类型进行处理，但保持在选择模式
		if (actualMergeType.value === 'no_merge' || actualMergeType.value === '') {
			// 不合并，直接返回排序后的列表
			return result;
		} else if (actualMergeType.value === 'merge_today') {
			// 合并当天任务 - 使用 store 中的方法
			return deliveryStore.mergeTodayDeliveries(result);
		} else {
			// 默认返回排序后的列表
			return result;
		}
	}

	return result;
});

// 处理派单点击事件
const handleDeliveryClick = (item: any) => {
	// 如果处于手动选择合并模式，点击派单时切换选中状态
	if (isManualMergeMode.value && !manualMergedDelivery.value) {
		// 切换选中状态
		const index = selectedDeliveryIds.value.indexOf(item.deliveryId);
		if (index === -1) {
			// 如果未选中，则添加到选中列表
			const newIds = [...selectedDeliveryIds.value, item.deliveryId];
			setSelectedDeliveryIds(newIds);
		} else {
			// 如果已选中，则从选中列表中移除
			const newIds = [...selectedDeliveryIds.value];
			newIds.splice(index, 1);
			setSelectedDeliveryIds(newIds);
		}
		return;
	}

	// 检查是否是合并的任务组
	if (item.isGrouped && item.groupItems && item.groupItems.length > 0) {
		// 获取所有派单ID
		const deliveryIds = item.groupItems.map((groupItem: any) => groupItem.deliveryId);

		// 更新选中的派单ID列表
		taskStore.setSelectedDeliveryIds(deliveryIds);

		// 使用switchTab跳转到任务页面
		uni.switchTab({
			url: '/pages/task/index',
		});
	} else {
		// 普通任务，直接跳转
		// 更新选中的派单ID
		taskStore.setSelectedDeliveryIds([item.deliveryId]);
		// 使用switchTab跳转到任务页面
		uni.switchTab({
			url: '/pages/task/index',
		});
	}
};

// 下拉刷新
const onRefresh = async () => {
	await refreshDeliveries();
};

// 处理图片加载错误
const handleImageError = (item: any) => {
	// 将图片URL设置为null，触发使用默认图标占位符
	if (item.currentContentImageUrl) {
		item.currentContentImageUrl = null;
	}
};

// 处理图片点击，放大查看
const handleImageClick = (item: any, event: any) => {
	// 阻止事件冒泡，避免触发派单点击事件
	event.stopPropagation();

	// 如果是组合派单或没有图片，不执行任何操作
	if (item.isGrouped || !item.currentContentImageUrl) {
		return;
	}

	// 使用uni.previewImage预览图片
	uni.previewImage({
		urls: [item.currentContentImageUrl],
		current: item.currentContentImageUrl,
		indicator: 'number',
		loop: false,
	});
};

// 处理下拉菜单点击
const toggleDropdown = () => {
	dropdownVisible.value = !dropdownVisible.value;
	// 这里可以添加下拉菜单的显示逻辑
};

// 处理搜索点击事件
const onSearchClick = (text: string) => {
	searchText.value = text;
};

// 处理刷新点击事件
const onRefreshClick = async () => {
	await refreshDeliveries();
};

// 处理设置点击事件
const onSettingsClick = () => {
	uni.navigateTo({
		url: '/pages/settings/index'
	});
};

// 取消手动合并
const cancelManualMerge = () => {
	// 清除选中状态
	setSelectedDeliveryIds([]);

	// 如果已经有合并后的派单，清除它
	if (manualMergedDelivery.value) {
		setManualMergedDelivery(null);
	}

	// 退出手动选择合并模式
	isManualMergeMode.value = false;

	// 重置为不合并模式
	setFilterSettings({ mergeType: 'no_merge' });
	actualMergeType.value = 'no_merge';
};

// 确认手动合并
const confirmManualMerge = () => {
	// 检查是否有选中的派单
	if (selectedDeliveryIds.value.length < 2) {
		showToast('请至少选择两个派单进行合并', 'none');
		return;
	}

	// 获取选中的派单
	const selectedDeliveries = getDeliveries.value.filter((item: any) =>
		selectedDeliveryIds.value.includes(item.deliveryId)
	);

	// 创建合并后的派单
	const firstDelivery = selectedDeliveries[0];
	const mergedDelivery = {
		...firstDelivery,
		deliveryId: `merged_${Date.now()}`, // 生成唯一ID
		isGrouped: true,
		groupItems: selectedDeliveries,
		deliveryName: getDeliveryName(firstDelivery.deliveryId),
		mergedCount: selectedDeliveries.length,
		deliveryDate: firstDelivery.deliveryDate,
		currentContentImageUrl: firstDelivery.currentContentImageUrl || null
	};

	// 保存合并后的派单
	setManualMergedDelivery(mergedDelivery);

	// 退出选择模式
	isManualMergeMode.value = false;

	// 设置为手动合并模式
	setFilterSettings({ mergeType: 'manual_merge' });
	actualMergeType.value = 'manual_merge';

	// 显示合并成功提示
	showToast(`已合并 ${selectedDeliveries.length} 个派单`, 'success');
};

onMounted(async () => {
	// 检查登录状态
	const authStore = useAuthStore();
	if (!(await authStore.checkAuth())) {
		uni.reLaunch({
			url: '/pages/login/login',
		});
		return;
	}

	// 加载派单数据
	await loadDeliveries();

	// 加载任务数据，用于统计
	await taskStore.loadTasks();

	// 调试信息
	console.log('📋 [DEBUG] 页面加载完成');
	console.log('📋 [DEBUG] dataVersion.value:', dataVersion.value);
	console.log('📋 [DEBUG] getDeliveries.value.length:', getDeliveries.value.length);
	console.log('📋 [DEBUG] deliveries.value.length:', deliveries.value.length);
	console.log('📋 [DEBUG] filteredDeliveries.value.length:', filteredDeliveries.value.length);
	console.log('📋 [DEBUG] taskStore.tasks.length:', taskStore.tasks.length);
	console.log('📋 [DEBUG] taskStore.hasData:', taskStore.hasData);

	// 测试统计数据
	if (getDeliveries.value.length > 0) {
		const firstDelivery = getDeliveries.value[0];
		console.log('📋 [DEBUG] 测试第一个派单的统计数据:', firstDelivery.deliveryId);
		const testStats = getDeliveryStats(firstDelivery);
		console.log('📋 [DEBUG] 第一个派单统计结果:', testStats);
	}
});
</script>

<style lang="scss" scoped>
.delivery-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	position: relative;

	.delivery-list {
		height: calc(100vh - 44px - var(--status-bar-height) - 50px);
		margin-top: calc(44px + var(--status-bar-height) + 50px);
		width: 100%;
		box-sizing: border-box;
		padding: 0;

		// 隐藏滚动条但保留滚动功能
		&::-webkit-scrollbar {
			display: none;
		}

		scrollbar-width: none;
		-ms-overflow-style: none;
	}

	.empty-state {
		padding: 2.5rem;
		text-align: center;
		color: #999;
		font-size: 1.125rem;

		.debug-info {
			display: block;
			margin-top: 1rem;
			font-size: 0.8rem;
			color: #666;
			background-color: #f0f0f0;
			padding: 0.5rem;
			border-radius: 4px;
		}
	}

	.delivery-items {
		padding: 0;
		margin: 0;
		width: 100%;

		&.has-merge-actions {
			padding-bottom: 80px; // 为底部操作栏留出空间
		}
	}

	.delivery-item {
		background-color: #ffffff;
		padding: 1.5rem 0.1rem;
		margin-top: 0.2rem;
		margin-bottom: 0.1rem;
		border-bottom: 1px solid rgba(0, 0, 0, 0.05);
		display: flex;
		align-items: stretch;
		width: 100%;
		min-height: 8rem;
		position: relative;

		&.checkbox-mode {
			padding-left: 3rem;
		}

		.delivery-checkbox {
			position: absolute;
			left: 1rem;
			top: 50%;
			transform: translateY(-50%);
			z-index: 2;
			width: 1.5rem;
			height: 1.5rem;
		}

		.delivery-left {
			position: relative;
			width: 20%;
			min-width: 5rem;
			max-width: 7.5rem;
			margin: 0 0.75rem;
			display: flex;
			align-items: center;
			justify-content: center;

			.task-image {
				width: 100%;
				height: auto;
				border-radius: 0.25rem;
				aspect-ratio: 1/1;
				object-fit: contain;
			}

			.default-image-placeholder {
				width: 100%;
				height: auto;
				border-radius: 0.25rem;
				aspect-ratio: 1/1;
				background-color: #ffffff;
				border: 1px solid #e0e0e0;
				display: flex;
				align-items: center;
				justify-content: center;

				.default-image-text {
					font-size: 80rpx;
					color: #e0e0e0;
				}
			}
		}

		.delivery-right {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			&.full-width {
				width: 100%;
				padding-left: 1rem;
			}

			.delivery-header {
				margin-bottom: 0.5rem;

				.delivery-title {
					display: flex;
					flex-wrap: wrap;
					align-items: center;
					gap: 0.25rem;
				}

				.delivery-name {
					font-weight: 700;
					color: #333;
					line-height: 1.3;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
					overflow: hidden;
					text-overflow: ellipsis;

					.merged-count {
						font-weight: normal;
						color: #666;
						font-size: 0.9rem;

						.count-number {
							font-weight: bold;
							color: #007aff;
						}
					}
				}
			}

			.delivery-stats {
				padding-right: 0.8rem;
				margin-bottom: 0.5rem;

				.stats-columns {
					display: flex;
					gap: 0.5rem;

					.stats-column {
						flex: 1;
						background-color: #f8f9fa;
						border-radius: 8px;
						padding: 8px;
						border: 1px solid #e9ecef;

						.column-header {
							text-align: center;
							margin-bottom: 6px;
							padding-bottom: 4px;
							border-bottom: 1px solid #dee2e6;

							.column-title {
								font-weight: 800;
								color: #495057;
								font-size: 0.85em;
								background-color: #e9ecef;
								padding: 2px 8px;
								border-radius: 4px;
								display: inline-flex;
								align-items: center;
								gap: 4px;

								.column-icon {
									font-size: 16px;
									margin-right: 2px;
								}

								.column-text {
									font-weight: 800;
								}
							}
						}

						// 左栏（点位）样式
						&.left-column {
							.column-title {
								background-color: rgba(13, 110, 253, 0.1);
								color: #0d6efd;
							}
						}

						// 右栏（实景）样式
						&.right-column {
							.column-title {
								background-color: rgba(111, 66, 193, 0.1);
								color: #6f42c1;
							}
						}

						.column-stats {
							display: flex;
							flex-direction: column;
							gap: 4px;
						}
					}
				}

				.status-item {
					margin-right: 0.75rem;
					color: #333;
					display: inline-flex;
					align-items: center;
					flex-wrap: nowrap;

					.status-value {
						font-weight: 700;
						margin-left: 2px;
						color: #333;

						&.warning-value {
							color: #ff0000;
						}
					}

					// 突出显示统计项
					&.highlight-stats-item {
						background-color: #f8f9fa;
						padding: 6px 10px;
						border-radius: 6px;
						margin-right: 8px;
						margin-bottom: 4px;
						border-left: 3px solid #e9ecef;
						box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

						.stats-label-highlight {
							font-weight: 800;
							color: #495057;
							font-size: 0.9em;
							margin-right: 4px;
						}

						.stats-value-highlight {
							font-weight: 900;
							font-size: 1.1em;
							text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
							padding: 2px 6px;
							border-radius: 4px;

							// 总数 - 蓝色
							&.total {
								color: #0d6efd;
								background-color: rgba(13, 110, 253, 0.15);
							}

							// 已完成 - 绿色
							&.completed {
								color: #198754;
								background-color: rgba(25, 135, 84, 0.15);
							}

							// 未完成 - 红色
							&.uncompleted {
								color: #dc3545;
								background-color: rgba(220, 53, 69, 0.15);
							}
						}

						// 紧凑模式样式（用于两栏布局）
						&.compact {
							background-color: transparent;
							padding: 3px 6px;
							margin-right: 0;
							margin-bottom: 2px;
							border-left: none;
							box-shadow: none;
							border-radius: 4px;
							display: flex;
							justify-content: space-between;
							align-items: center;
							width: 100%;

							.stats-label-highlight {
								font-size: 0.8em;
								text-align: left;
								flex-shrink: 0;
							}

							.stats-value-highlight {
								font-size: 1em;
								text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
								text-align: center;
								flex: 1;
								margin-left: 8px;

								// 在紧凑模式下使用更鲜艳的颜色，但不要底色
								&.total {
									color: #0056b3;
								}

								&.completed {
									color: #155724;
								}

								&.uncompleted {
									color: #721c24;
								}
							}
						}

						// 未完成数量为 0 时的灰色样式
						&.zero-uncompleted {
							.stats-label-highlight.zero-uncompleted-label {
								color: #999999 !important;
							}

							.stats-value-highlight.zero-uncompleted-value {
								color: #999999 !important;
								background-color: transparent !important;
							}
						}
					}
				}
			}

			.delivery-info {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.task-time {
					color: #666;
					padding: 2px 0;
				}

				.queue-id {
					color: #666;
					padding: 2px 0;
					font-size: 0.85em;
				}
			}
		}
	}

	// 底部操作栏样式
	.merge-actions {
		position: fixed;
		bottom: var(--window-bottom, 0);
		left: 0;
		right: 0;
		height: 60px;
		background-color: #ffffff;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
		z-index: 9999;

		.action-button {
			flex: 1;
			height: 100%;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			padding: 0 20px;
			transition: all 0.2s ease;

			&:active {
				opacity: 0.7;
			}

			&.cancel {
				color: #dc3545;
				border-right: 1px solid #eee;
			}

			&.confirm {
				color: #28a745;
			}

			.action-icon {
				font-size: 1.25rem;
				margin-right: 0.5rem;
			}

			.action-text {
				font-size: 1rem;
				font-weight: 500;
			}
		}
	}
}
</style>
