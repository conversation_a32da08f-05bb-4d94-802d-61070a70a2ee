<template>
  <view class="task-navbar" :style="dynamicFontStyle">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- 主导航栏 -->
    <view class="navbar-content">
      <!-- 左侧返回按钮区 -->
      <view class="navbar-left">
        <view class="back-button" @click="onBackClick">
          <text class="iconfont icon-back"></text>
        </view>
      </view>

      <!-- 中间数量显示和搜索区 -->
      <view class="navbar-center">
        <view class="center-content">
          <view v-if="!showSearchInput" class="counts-container">
            <!-- 多个派单时：合单信息和任务统计分两行显示 -->
            <template v-if="isMultipleDeliveries">
              <view class="multi-line-layout">
                <view class="count-text delivery-line">
                  <text>合单: </text>
                  <text class="stats-number">{{ deliveryCount }}</text>
                  <text>（{{ deliveryName }}）等</text>
                </view>
                <text class="count-text stats-line">
                  <text class="stats-label">任务:</text><text class="stats-number">{{ totalCount }}</text>
                  <text class="stats-label">当前:</text><text class="stats-number">{{ currentCount }}</text>
                </text>
              </view>
            </template>
            <!-- 单个派单时：派单名单独一行，任务和当前一行 -->
            <template v-else-if="deliveryName">
              <view class="multi-line-layout">
                <view class="count-text delivery-line">
                  <text>{{ deliveryName }}</text>
                </view>
                <text class="count-text stats-line">
                  <text class="stats-label">任务:</text><text class="stats-number">{{ totalCount }}</text>
                  <text class="stats-label">当前:</text><text class="stats-number">{{ currentCount }}</text>
                </text>
              </view>
            </template>
            <!-- 没有派单时：只显示任务和当前 -->
            <template v-else>
              <text class="count-text single-line">
                <text class="stats-label">任务:</text><text class="stats-number">{{ totalCount }}</text>
                <text class="stats-label">当前:</text><text class="stats-number">{{ currentCount }}</text>
              </text>
            </template>
          </view>
          <view v-if="!showSearchInput" class="search-icon" @click="toggleSearchInput">
            <text class="iconfont icon-search"></text>
          </view>

          <!-- 搜索输入框 -->
          <view v-if="showSearchInput" class="search-input-container">
            <input
              class="search-input"
              type="text"
              v-model="searchText"
              placeholder="搜索"
              confirm-type="search"
              @confirm="handleSearch"
              focus
            />
            <view class="search-actions">
              <view class="search-button" @click="handleSearch">
                <text class="iconfont icon-search"></text>
              </view>
              <view class="close-button" @click="toggleSearchInput">
                <text class="iconfont icon-close"></text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 右侧筛选设置区 -->
      <view class="navbar-right">
        <view class="filter-icons">
          <view class="filter-icon" @click="onConfigClick">
            <text class="iconfont icon-settings"></text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';

// 声明全局 uni 对象，解决 TypeScript 错误
declare const uni: any;

// 定义组件属性
const props = defineProps({
  totalCount: {
    type: [Number, String],
    default: 0
  },
  currentCount: {
    type: [Number, String],
    default: 0
  },
  deliveryName: {
    type: String,
    default: ''
  },
  deliveryCount: {
    type: [Number, String],
    default: 0
  }
});

// 计算是否有多个派单
const isMultipleDeliveries = computed(() => {
  return Number(props.deliveryCount) > 1;
});

// 注意：派单名称现在使用CSS文本溢出处理，不再需要JavaScript截取

// 定义事件
const emit = defineEmits(['search', 'config', 'back']);

// 状态栏高度
const statusBarHeight = ref(20);

// 从本地存储加载搜索框状态
const loadSearchState = () => {
  try {
    const showSearch = uni.getStorageSync('task_navbar_show_search');
    const searchValue = uni.getStorageSync('task_navbar_search_text');
    return {
      showSearch: showSearch === 'true',
      searchText: searchValue || ''
    };
  } catch (error) {
    console.warn('加载搜索框状态失败:', error);
    return { showSearch: false, searchText: '' };
  }
};

// 保存搜索框状态到本地存储
const saveSearchState = (show: boolean, text: string) => {
  try {
    uni.setStorageSync('task_navbar_show_search', show.toString());
    uni.setStorageSync('task_navbar_search_text', text);
  } catch (error) {
    console.warn('保存搜索框状态失败:', error);
  }
};

// 加载搜索框状态
const searchState = loadSearchState();

// 是否显示搜索输入框
const showSearchInput = ref(searchState.showSearch);
// 搜索文本
const searchText = ref(searchState.searchText);

// 字体大小管理
const currentFontScale = ref(1);

// 动态字体样式
const dynamicFontStyle = computed(() => ({
  '--font-scale': currentFontScale.value
}));

// 初始化字体大小
const initFontSize = () => {
  try {
    const savedFontScale = uni.getStorageSync('fontScale') || 1;
    currentFontScale.value = savedFontScale;
    console.info(`📄 [TASK-NAVBAR] 组件字体大小初始化: 缩放 ${savedFontScale}`);
  } catch (error) {
    console.error('组件字体大小初始化失败:', error);
  }
};

// 处理字体大小变化
const handleFontSizeChange = (data: any) => {
  const { size, scale } = data;
  currentFontScale.value = scale;
  console.info(`📄 [TASK-NAVBAR] 组件字体大小已更新: ${size} (缩放: ${scale})`);
};

// 获取状态栏高度
onMounted(() => {
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;

  // 初始化字体大小
  initFontSize();

  // 监听字体大小变化
  uni.$on('fontSizeChanged', handleFontSizeChange);
});

onUnmounted(() => {
  // 清理事件监听
  uni.$off('fontSizeChanged', handleFontSizeChange);
});

// 切换搜索输入框显示状态
const toggleSearchInput = () => {
  console.info('🔍 [TASK-NAVBAR] 切换搜索框状态，当前显示:', showSearchInput.value);
  showSearchInput.value = !showSearchInput.value;
  if (!showSearchInput.value) {
    // 如果隐藏搜索框，清空搜索内容并发送空搜索
    console.info('🔍 [TASK-NAVBAR] 关闭搜索框，清空搜索内容');
    searchText.value = '';
    emit('search', '');
  }
  // 保存搜索框状态
  saveSearchState(showSearchInput.value, searchText.value);
};

// 处理搜索
const handleSearch = () => {
  emit('search', searchText.value);
  // 搜索后关闭搜索框
  showSearchInput.value = false;
  // 保存搜索框状态
  saveSearchState(showSearchInput.value, searchText.value);
};

// 处理设置点击
const onConfigClick = () => {
  emit('config');
};

// 处理返回按钮点击
const onBackClick = () => {
  emit('back');
};
</script>

<style lang="scss" scoped>
.task-navbar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;

  // 应用字体缩放到文本元素
  .count-text, .highlight-text, .icon {
    font-size: calc(1em * var(--font-scale, 1)) !important;
  }

  .status-bar {
    background-color: #6CA5F2; // 深绿色状态栏
  }

  .navbar-content {
    height: 44px;
    background-color: #6CA5F2; // 蓝色导航栏
    display: flex;
    align-items: center;
    padding: 0 15px;

    .navbar-left {
      width: 50px;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .back-button {
        width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        pointer-events: auto;

        .iconfont {
          color: #FFFFFF;
          font-weight: 700;
          font-size: 18px;
          transform: scale(1.2);
          pointer-events: none;
        }

        /* 添加点击反馈 */
        &:active {
          transform: scale(0.95);
          opacity: 0.8;
        }
      }
    }

    .navbar-center {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 10px;
      min-width: 0;

      .center-content {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        flex: 1;
        min-width: 0;
      }

      .counts-container {
        display: flex;
        align-items: center;
        margin-right: 10px;
        flex: 1;
        min-width: 0;

        // 单行布局
        .count-text.single-line {
          color: #FFFFFF;
          font-weight: 600;
          text-align: center;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          justify-content: center;

          .highlight-text {
            color: #FF4D4F;
            font-weight: 700;
          }

          // 突出显示统计数字
          .stats-highlight {
            background-color: rgba(255, 77, 79, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 900;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
          }

          .stats-label {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            margin: 0 2px;
          }

          .stats-number {
            color: #FFD700; // 金色
            font-weight: 900;
            font-size: 1.1em;
            background-color: rgba(255, 215, 0, 0.2);
            padding: 1px 4px;
            border-radius: 3px;
            margin: 0 4px;
          }
        }

        // 多行布局
        .multi-line-layout {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          line-height: 1.2;
          width: 100%;
          max-width: 100%;

          .count-text {
            color: #FFFFFF;
            font-weight: 600;
            text-align: left;
            margin: 1px 0;
            width: 100%;

            &.delivery-line {
              font-size: 0.9em;
              color: rgba(255, 255, 255, 0.95);
              font-weight: 700;
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: flex-start;
              overflow: hidden;
              white-space: nowrap;

              text {
                flex-shrink: 0;

                &:last-child {
                  flex-shrink: 1;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  min-width: 0;
                }
              }

              .stats-number {
                color: #FFD700;
                font-weight: 900;
                font-size: 1.1em;
                padding: 1px 4px;
                border-radius: 3px;
                margin: 0 3px;
                flex-shrink: 0;
              }
            }

            &.stats-line {
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 0.85em;

              .stats-label {
                color: rgba(255, 255, 255, 0.9);
                font-weight: 600;
                margin: 0 2px;
              }

              .stats-number {
                color: #FFD700;
                font-weight: 900;
                font-size: 1.1em;
                padding: 1px 4px;
                border-radius: 3px;
                margin: 0 3px;
              }
            }
          }
        }
      }

      .search-icon {
        width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        pointer-events: auto;

        .iconfont {
          color: #FFFFFF;
          font-size: 16px;
          transform: scale(1.1);
          pointer-events: none;
        }

        /* 添加点击反馈 */
        &:active {
          transform: scale(0.95);
          opacity: 0.8;
        }
      }

      .search-input-container {
        display: flex;
        flex: 1;
        height: 36px;
        background-color: transparent;
        padding: 0;
        align-items: center;
        justify-content: center;
        width: 100%;

        .search-input {
          flex: 1;
          height: 36px;
          color: #FFFFFF;
          background-color: transparent;
          border: none;
          border-bottom: 1px solid rgba(255, 255, 255, 0.7);
          padding: 0 5px;
        }

        .search-actions {
          display: flex;
          align-items: center;

          .search-button, .close-button {
            width: 36px;
            height: 36px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            pointer-events: auto;

            .iconfont {
              font-size: 16px;
              transform: scale(1.1);
              color: #FFFFFF;
              pointer-events: none;
            }

            /* 添加点击反馈 */
            &:active {
              transform: scale(0.95);
              opacity: 0.8;
            }
          }
        }
      }
    }

    .navbar-right {
      width: 50px;
      display: flex;
      justify-content: flex-end;

      .filter-icons {
        display: flex;
        flex-direction: row;
        align-items: center;

        .filter-icon {
          width: 36px;
          height: 36px;
          margin-left: 5px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          pointer-events: auto;
          z-index: 10;

          .iconfont {
            color: #FFFFFF;
            font-weight: 700;
            font-size: 18px;
            transform: scale(1.2);
            pointer-events: none;
          }

          /* 添加点击反馈 */
          &:active {
            transform: scale(0.95);
            opacity: 0.8;
          }
        }
      }
    }
  }
}
</style>
