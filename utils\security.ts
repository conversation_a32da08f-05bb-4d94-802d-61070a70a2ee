import { ENVIRONMENT } from './env';

/**
 * 生产环境安全配置
 */
export class SecurityConfig {
  /**
   * 检查是否为生产环境
   */
  static isProduction(): boolean {
    return ENVIRONMENT.isProduction;
  }

  /**
   * 生产环境安全检查
   */
  static performSecurityCheck(): void {
    if (!this.isProduction()) {
      return;
    }

    // 1. 禁用调试模式
    this.disableDebugMode();

    // 2. 清理敏感信息
    this.clearSensitiveData();

    // 3. 启用安全策略
    this.enableSecurityPolicies();
  }

  /**
   * 禁用调试模式
   */
  private static disableDebugMode(): void {
    // 重写console方法（生产环境）
    if (this.isProduction()) {
      const noop = () => {};
      console.info = noop;
      console.debug = noop;
      console.info = noop;
      // 保留warn和error用于关键问题追踪
    }
  }

  /**
   * 清理敏感数据
   */
  private static clearSensitiveData(): void {
    // 清理可能的敏感信息
    try {
      // 清理开发环境的测试数据
      uni.removeStorageSync('dev_test_data');
      uni.removeStorageSync('debug_info');
    } catch (error) {
      // 静默处理
    }
  }

  /**
   * 启用安全策略
   */
  private static enableSecurityPolicies(): void {
    // 启用SSL验证
    // 注意：在request.ts中已设置sslVerify: false，生产环境可能需要调整
    
    // 设置请求超时
    // 在生产环境中使用更严格的超时设置
  }

  /**
   * 数据加密（如果需要）
   */
  static encryptSensitiveData(data: string): string {
    if (!this.isProduction()) {
      return data; // 开发环境不加密
    }
    
    // 这里可以添加加密逻辑
    // 例如使用uni-app的加密API或第三方加密库
    return data;
  }

  /**
   * 数据解密（如果需要）
   */
  static decryptSensitiveData(encryptedData: string): string {
    if (!this.isProduction()) {
      return encryptedData; // 开发环境不解密
    }
    
    // 这里可以添加解密逻辑
    return encryptedData;
  }

  /**
   * 错误报告（生产环境）
   */
  static reportError(error: Error, context?: string): void {
    if (this.isProduction()) {
      // 在生产环境中，可以将错误发送到错误监控服务
      // 例如：Sentry, Bugsnag等
      console.error(`Production Error [${context}]:`, error);
      
      // 这里可以添加错误上报逻辑
      // 例如：发送到错误监控API
    }
  }
}

// 应用启动时执行安全检查
SecurityConfig.performSecurityCheck();
