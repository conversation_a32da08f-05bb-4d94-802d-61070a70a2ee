export interface LoginCredentials {
	username : string;
	password : string;
}

export interface UserInfo {
	sub: string;
	openid: string;
	roles: string[];
	iss: string;
	active: boolean;
	employeeId: string | null;
	avatar: string | null;
	token_type: string;
	client_id: string;
	aud: string[];
	license: string;
	nbf: number;
	scope: string;
	exp: number;
	iat: number;
	jti: string;
	nickname?: string;
}

export interface AuthResponse {
	access_token : string;
	refresh_token : string;
	openid : string;
}