import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Delivery } from '@/types';
import { showToast, formatDate } from '@/utils';
import { useAuthStore } from './auth';
import { useSettingsStore } from './settings';
import { getDateRange } from '@/utils/date';
import { MobileResult } from '../types/MobileResult';
import { Task, TaskImages } from '../types';
import { ClientStore, DeliveryState, TaskState } from '../types/ClientStore';
import { MobileApi } from '../utils/api';

// 声明全局 uni 对象
declare const uni: any;

export const useDeliveryStore = defineStore('delivery', () => {
	// 静态数据存储
	const clientStore = {
		// 保存派单信息，来源于本地缓存或服务端
		deliveryMap: new Map<string, Delivery>(),
		// 保存任务信息，来源于本地缓存或服务端
		taskMap: new Map<string, Task>(),
		// 保存图片信息，来源于本地缓存或服务端
		imageMap: new Map<string, TaskImages>(),
		// 保存上传信息，来源于用户拍照
		uploadMap: new Map<string, TaskImages>()
	} as ClientStore;

	// 派单状态数据（响应式）
	const deliveryStates = ref(new Map<string, DeliveryState>());

	const taskStates = ref(new Map<string, TaskState>());

	const uploadState = ref({
		uploadingCount: 0,
		uploadedCount: 0,
		uploadFailedCount: 0,
		uploadingQueue: []
	})

	// 是否有数据
	const hasData = ref(false);
	// 是否正在加载
	const loading = ref(false);
	// 是否正在刷新
	const refreshing = ref(false);
	// 数据版本号，用于触发响应式更新
	const dataVersion = ref(0);

	// 获取所有派单
	const getDeliveries = computed(() => {
		// 通过访问 dataVersion.value 来建立响应式依赖
		dataVersion.value;
		return Array.from(clientStore.deliveryMap.values());
	});

	// 获取派单任务
	const getDeliveryTasks = computed(() => {
		return Array.from(clientStore.taskMap.values());
	});

	// 获取任务图片
	const getTaskImages = computed(() => {
		return Array.from(clientStore.imageMap.values());
	});

	// 获取派单状态
	const getDeliveryState = computed(() => (deliveryId: string): DeliveryState => {
		return deliveryStates.value.get(deliveryId) || {
			totalCount: 0,
			completeCount: 0,
			uncompleteCount: 0,
		spotTotalCount: 0,
			spotCompletedCount: 0,
			spotUncompleteCount: 0,
			zoneTotalCount: 0,
			zoneCompletedCount: 0,
			zoneUncompleteCount: 0
		};
	});

	// 初始化派单状态
	const initDeliveryStates = () => {
		const newStates = new Map<string, DeliveryState>();
		clientStore.deliveryMap.forEach((delivery, deliveryId) => {
			newStates.set(deliveryId, {
				totalCount: delivery.totalCount || 0,
				completeCount: delivery.completeCount || 0,
				uncompleteCount: delivery.totalCount - delivery.completeCount,
			spotTotalCount: delivery.spotTaskCount || 0,
				spotCompletedCount: delivery.spotCompletedCount || 0,
				spotUncompleteCount: delivery.spotTaskCount - delivery.spotCompletedCount,
				zoneTotalCount: delivery.zoneTotalCount || 0,
				zoneCompletedCount: delivery.zoneCompletedCount || 0,
				zoneUncompleteCount: delivery.zoneTotalCount - delivery.zoneCompletedCount
			});
		});
		deliveryStates.value = newStates;
	};

	// 初始化任务状态
	const initTaskStates = () => {
		const newStates = new Map<string, TaskState>();
		clientStore.taskMap.forEach((task, taskId) => {
			newStates.set(taskId, {
				requiredCount: task.photoMax || 0,
				hasTaken: task.hasTaken || 0,
				hasPending: 0
			});
		})
		// TODO 从本地缓存加载 hasPending
	}
	// 初始化本地照片
	const initUploadState = () => {
		let pendingCount = 0, queueCount = 0, failedCount = 0;
		clientStore.uploadMap.forEach((image) => {
			if (image.imageStatus === 'pending') {
				pendingCount++;
			} else if (image.imageStatus === 'uploading') {
				queueCount++;
			} else if (image.imageStatus === 'failed') {
				failedCount++;
			}
		});
		uploadState.value = {
			uploadingCount: queueCount,
			uploadedCount: 0, // 这个值会在上传成功后更新
			uploadFailedCount: failedCount,
			uploadingQueue: [] // 这个队列会在上传过程中动态管理
		};
	}

	// 本地缓存key
	const CACHE_KEY = 'delivery_cache';
	const SELECTED_IDS_KEY = 'delivery_selected_ids';
	const MANUAL_MERGED_KEY = 'delivery_manual_merged';
	const FILTER_SETTINGS_KEY = 'delivery_filter_settings';

	// 选中的派单ID列表
	const selectedDeliveryIds = ref<string[]>([]);
	// 手动合并后的派单
	const manualMergedDelivery = ref<any>(null);
	// 筛选设置
	const filterSettings = ref({
		mergeType: 'no_merge', // 合并类型：no_merge, merge_today, manual_merge
		sortOrder: 'time_asc', // 排序方式：time_asc, time_desc
	});

	// 保存到缓存
	const saveToCache = () => {
		if (clientStore.deliveryMap.size === 0) return;
		try {
			// 保存筛选设置
			uni.setStorageSync(FILTER_SETTINGS_KEY, filterSettings.value);

			// 保存本地数据
			const cacheData = {
				deliveries: Array.from(clientStore.deliveryMap.values()),
				tasks: Array.from(clientStore.taskMap.values()),
				images: Array.from(clientStore.imageMap.values()),
				uploadMap: Array.from(clientStore.uploadMap.values()),

				deliveryStates: Object.fromEntries(deliveryStates.value),
				taskStates: Object.fromEntries(taskStates.value),

			};

			uni.setStorageSync(CACHE_KEY, cacheData);
			console.info('📋 [DELIVERY] 派单数据已缓存');
		} catch (error) {
			console.error('❌ [DELIVERY] 缓存派单数据失败:', error);
		}
	};

	// 从缓存加载
	const loadFromCache = () => {
		try {
			// 加载筛选设置
			const savedFilterSettings = uni.getStorageSync(FILTER_SETTINGS_KEY);
			if (savedFilterSettings) {
				filterSettings.value = savedFilterSettings;
			}

			const cache = uni.getStorageSync(CACHE_KEY);
			if (cache) {
				// 清空现有数据
				clientStore.deliveryMap.clear();
				clientStore.taskMap.clear();
				clientStore.imageMap.clear();
				clientStore.uploadMap.clear();

				// 恢复数据
				cache.deliveries?.forEach((delivery: Delivery) => {
					if (delivery.deliveryId) {
						clientStore.deliveryMap.set(delivery.deliveryId, delivery);
					}
				});
				cache.tasks?.forEach((task: Task) => {
					if (task.taskId) {
						clientStore.taskMap.set(task.taskId, task);
					}
				});
				cache.images?.forEach((image: TaskImages) => {
					if (image.id) {
						clientStore.imageMap.set(image.id, image);
					}
				});
				cache.uploadMap?.forEach((image: TaskImages) => {
					if (image.id) {
						clientStore.uploadMap.set(image.id, image);
					}
				});


				// 初始化派单状态
				initDeliveryStates();
				// 初始化任务状态
				initTaskStates();
				// 初始化本地照片
				initUploadState();

				hasData.value = true;
				// 触发响应式更新
				dataVersion.value++;
				console.info('📋 [DELIVERY] 从缓存加载了「' + clientStore.deliveryMap.size + '」条派单');
				console.info('📋 [DELIVERY] 从缓存加载了「' + clientStore.taskMap.size + '」条任务');
				console.info('📋 [DELIVERY] 从缓存加载了「' + clientStore.imageMap.size + '」条上传后照片');
				console.info('📋 [DELIVERY] 从缓存加载了「' + clientStore.uploadMap.size + '」条本地照片');
				return true;
			}
		} catch (error) {
			console.error('❌ [DELIVERY] 加载缓存派单数据失败:', error);
		}
		return false;
	};

	// 加载派单任务
	const loadDeliveries = async (forceLoad = false) => {
		// 如果已有数据且不是强制加载，尝试从缓存加载
		if (hasData.value && !forceLoad) {
			if (loadFromCache()) return;
		}

		if (!refreshing.value) {
			loading.value = true;
		}

		try {
			const authStore = useAuthStore();
			const settingsStore = useSettingsStore();
			const username = authStore.getUsername();

			// 验证用户名是否有效
			if (!username || username.trim() === '') {
				console.error('❌ [DELIVERY] 用户名为空，无法加载派单');
				showToast('用户信息无效，请重新登录', 'none');
				return;
			}

			// 根据设置获取时间范围
			const { startDay, endDay } = getDateRange(settingsStore.settings.orderPeriod);

			console.info(`📋 [DELIVERY] 开始加载派单: 用户=${username}, 时间范围=${startDay} ~ ${endDay}`);

			const response = await MobileApi.loadMobileTasks(
				username,
				startDay,
				endDay
			);

			const mobileResult = response?.data as MobileResult;

			// 清空现有数据
			clientStore.deliveryMap.clear();
			clientStore.taskMap.clear();
			clientStore.imageMap.clear();

			// 更新静态数据
			mobileResult.deliveries?.forEach(delivery => {
				if (delivery.deliveryId) {
					clientStore.deliveryMap.set(delivery.deliveryId, delivery);
				}
			});
			mobileResult.tasks?.forEach(task => {
				if (task.taskId) {
					clientStore.taskMap.set(task.taskId, task);
				}
			});
			mobileResult.images?.forEach(image => {
				if (image.id) {
					clientStore.imageMap.set(image.id, image);
				}
			})

			// 初始化派单状态
			initDeliveryStates();
			initTaskStates();

			hasData.value = true;
			// 触发响应式更新
			dataVersion.value++;
			saveToCache();
			console.info('📋加载了「' + mobileResult.deliveries?.length + '」条派单');
			console.info('📋加载了「' + mobileResult.tasks?.length + '」条任务');
			console.info('📋加载了「' + mobileResult.images?.length + '」条照片');
		} catch (error) {
			console.error('❌ [DELIVERY] 加载派单失败:', error);
		} finally {
			loading.value = false;
		}
	};

	// 刷新派单任务
	const refreshDeliveries = async () => {
		try {
			refreshing.value = true;
			await loadDeliveries(true);
		} finally {
			refreshing.value = false;
		}
	};

	// 获取派单对应的派单名称
	const getDeliveryName = (deliveryId: string) => {
		let delivery = clientStore.deliveryMap.get(deliveryId);
		const nameParts = [
			delivery?.deliveryName || '',
			delivery?.photoRequirements ? `(${delivery.photoRequirements})` : '',
		].filter((part) => part && part.trim());
		return nameParts.join(' ') || '未设置';
	};

	// 从本地存储加载状态
	const loadStateFromStorage = () => {
		try {
			// 加载选中的派单ID列表
			const savedSelectedIds = uni.getStorageSync(SELECTED_IDS_KEY);
			if (savedSelectedIds) {
				selectedDeliveryIds.value = JSON.parse(savedSelectedIds);
			}

			// 加载手动合并后的派单
			const savedManualMerged = uni.getStorageSync(MANUAL_MERGED_KEY);
			if (savedManualMerged) {
				manualMergedDelivery.value = JSON.parse(savedManualMerged);
			}

			// 加载筛选设置
			const savedFilterSettings = uni.getStorageSync(FILTER_SETTINGS_KEY);
			if (savedFilterSettings) {
				filterSettings.value = JSON.parse(savedFilterSettings);
			}
		} catch (error) {
			console.warn('加载状态失败:', error);
		}
	};

	// 保存状态到本地存储
	const saveStateToStorage = () => {
		try {
			// 保存选中的派单ID列表
			uni.setStorageSync(SELECTED_IDS_KEY, JSON.stringify(selectedDeliveryIds.value));

			// 保存手动合并后的派单
			uni.setStorageSync(MANUAL_MERGED_KEY, JSON.stringify(manualMergedDelivery.value));

			// 保存筛选设置
			uni.setStorageSync(FILTER_SETTINGS_KEY, JSON.stringify(filterSettings.value));
		} catch (error) {
			console.warn('保存状态失败:', error);
		}
	};

	// 设置选中的派单ID列表
	const setSelectedDeliveryIds = (ids: string[]) => {
		selectedDeliveryIds.value = ids;
		saveStateToStorage(); // 保存到本地存储
	};

	// 获取选中的派单ID列表
	const getSelectedDeliveryIds = () => selectedDeliveryIds.value;

	// 设置手动合并后的派单
	const setManualMergedDelivery = (delivery: any) => {
		manualMergedDelivery.value = delivery;
		saveStateToStorage(); // 保存到本地存储
	};

	// 获取手动合并后的派单
	const getManualMergedDelivery = () => manualMergedDelivery.value;

	// 设置筛选设置
	const setFilterSettings = (settings: any) => {
		filterSettings.value = { ...filterSettings.value, ...settings };
		saveStateToStorage(); // 保存到本地存储
	};

	// 获取筛选设置
	const getFilterSettings = () => filterSettings.value;

	// 合并当天任务的辅助函数
	const mergeTodayDeliveries = (deliveryList: Delivery[]) => {
		// 按日期分组，同一天的任务合并显示
		const groupedByDate: Record<string, any> = {};

		deliveryList.forEach(item => {
			// 获取日期部分（不含时间）
			const dateStr = item.deliveryDate ? new Date(item.deliveryDate).toISOString().split('T')[0] : 'unknown';

			if (!groupedByDate[dateStr]) {
				// 创建新组
				groupedByDate[dateStr] = {
					...item,
					isGrouped: true,
					groupItems: [item],
					deliveryName: getDeliveryName(item.deliveryId), // 只使用第一个派单的名称
					mergedCount: 1, // 初始化合并数量
				};
			} else {
				// 添加到现有组
				const group = groupedByDate[dateStr];
				group.groupItems.push(item);
				group.mergedCount = group.groupItems.length; // 更新合并数量
			}
		});

		// 转换回数组
		return Object.values(groupedByDate);
	};

	// 根据筛选条件过滤和排序派单列表
	const getFilteredDeliveries = (searchText = '', taskStore?: any) => {
		// 首先应用搜索过滤
		let filtered = getDeliveries.value;

		// 应用搜索文本过滤
		if (searchText) {
			const searchLower = searchText.toLowerCase().trim();
			const searchTerms = searchLower.split(/\s+/); // 支持多个搜索词
			filtered = filtered.filter((item: Delivery) => {
				// 构建搜索文本
				const searchableText = [
					getDeliveryName(item.deliveryId),
					item.deliveryId?.toString(),
					item.queueId?.toString(),
					formatDate(item.deliveryDate) // 添加日期搜索支持
				].filter(Boolean).join(' ').toLowerCase();

				// 所有搜索词都必须匹配
				return searchTerms.every(term => searchableText.includes(term));
			});
		}

		// 应用完成状态过滤
		const settingsStore = useSettingsStore();
		const completionStatus = settingsStore.settings.deliveryDisplaySettings?.completionStatus || 'incomplete';

		if (completionStatus !== 'all' && taskStore) {
			filtered = filtered.filter((item: Delivery) => {
				// 使用 getDeliveryState 获取准确的任务统计
				const stats = taskStore.getDeliveryState(item.deliveryId);

				// 如果是组合派单，检查所有子派单的完成状态
				if (item.isGrouped && item.groupItems) {
					const groupStats = item.groupItems.map(groupItem =>
						taskStore.getDeliveryState(groupItem.deliveryId)
					);

					// 所有子派单都完成才算完成
					const isComplete = groupStats.every(stat =>
						stat.requiredCount > 0 &&
						(stat.uploadedCount + stat.pendingCount) >= stat.requiredCount
					);

					return completionStatus === 'complete' ? isComplete : !isComplete;
				}

				// 单个派单的完成状态判断
				const isComplete = stats.requiredCount > 0 &&
					(stats.uploadedCount + stats.pendingCount) >= stats.requiredCount;

				return completionStatus === 'complete' ? isComplete : !isComplete;
			});
		}

		// 应用排序
		const sorted = [...filtered].sort((a: Delivery, b: Delivery) => {
			// 首先按照队列ID排序
			if (a.queueId && b.queueId && a.queueId !== b.queueId) {
				return a.queueId.localeCompare(b.queueId);
			}
			// 然后按照日期排序
			const dateA = new Date(a.deliveryDate).getTime();
			const dateB = new Date(b.deliveryDate).getTime();
			const dateCompare = filterSettings.value.sortOrder === 'time_asc' ? dateA - dateB : dateB - dateA;

			// 如果日期相同，按照派单ID排序
			if (dateCompare === 0) {
				return a.deliveryId.localeCompare(b.deliveryId);
			}
			return dateCompare;
		});

		// 根据合并类型进行处理
		switch (filterSettings.value.mergeType) {
			case 'manual_merge':
				// 如果有手动合并的结果，则只显示手动合并的结果
				return manualMergedDelivery.value ? [manualMergedDelivery.value] : sorted;

			case 'merge_today':
				// 合并当天任务
				return mergeTodayDeliveries(sorted);

			case 'no_merge':
			default:
				// 不合并，直接返回排序后的列表
				return sorted;
		}
	};

	// 在loadDeliveries后重新计算手动合并的派单
	const recalculateManualMergedDelivery = () => {
		// 如果没有手动合并的派单，直接返回
		if (!manualMergedDelivery.value) return;

		// 获取手动合并的派单中的所有派单ID
		const mergedIds = manualMergedDelivery.value.groupItems.map((item: any) => item.deliveryId);

		// 检查这些ID是否仍然存在于当前的派单列表中
		const existingDeliveries = getDeliveries.value.filter((item: any) =>
			mergedIds.includes(item.deliveryId)
		);

		// 如果没有找到任何派单，清空手动合并的派单
		if (existingDeliveries.length === 0) {
			manualMergedDelivery.value = null;
			return;
		}

		// 如果找到的派单数量与原来不同，需要重新创建手动合并的派单
		if (existingDeliveries.length !== mergedIds.length) {
			// 创建新的手动合并派单
			const firstDelivery = existingDeliveries[0];
			manualMergedDelivery.value = {
				...firstDelivery,
				deliveryId: `merged_${Date.now()}`, // 生成唯一ID
				isGrouped: true,
				groupItems: existingDeliveries,
				deliveryName: getDeliveryName(firstDelivery.deliveryId),
				mergedCount: existingDeliveries.length,
				deliveryDate: firstDelivery.deliveryDate,
				currentContentImageUrl: firstDelivery.currentContentImageUrl
			};
		}
	};

	// 在loadDeliveries后调用
	const afterLoadDeliveries = () => {
		// 重新计算手动合并的派单
		recalculateManualMergedDelivery();
	};

	// 更新任务的 taskImages 数组
	const updateTaskImages = (taskId: string | number, newImage: any) => {
		try {
			const task = clientStore.taskMap.get(taskId.toString());
			if (task) {
				// 确保 taskImages 数组存在
				if (!task.taskImages) {
					task.taskImages = [];
				}

				// 添加新图片到数组
				task.taskImages.push(newImage);

				// 更新 Map 中的任务
				clientStore.taskMap.set(taskId.toString(), task);

				// 保存到缓存
				saveToCache();

				// 触发响应式更新
				dataVersion.value++;

				console.log(`📋 [DELIVERY] 任务 ${taskId} 图片更新成功，当前图片数: ${task.taskImages.length}`);
				return true;
			} else {
				console.warn(`📋 [DELIVERY] 未找到任务 ${taskId}`);
				return false;
			}
		} catch (error) {
			console.error(`📋 [DELIVERY] 更新任务 ${taskId} 图片失败:`, error);
			return false;
		}
	};

	// 更新任务状态
	const updateTaskStatus = (taskId: string | number, newStatus: string) => {
		try {
			const task = clientStore.taskMap.get(taskId.toString());
			if (task) {
				const oldStatus = task.taskStatus;
				task.taskStatus = newStatus;

				// 更新 Map 中的任务
				clientStore.taskMap.set(taskId.toString(), task);

				// 保存到缓存
				saveToCache();

				// 触发响应式更新
				dataVersion.value++;

				console.log(`📋 [DELIVERY] 任务 ${taskId} 状态更新成功: ${oldStatus} → ${newStatus}`);
				return true;
			} else {
				console.warn(`📋 [DELIVERY] 未找到任务 ${taskId}`);
				return false;
			}
		} catch (error) {
			console.error(`📋 [DELIVERY] 更新任务 ${taskId} 状态失败:`, error);
			return false;
		}
	};

	// 包装loadDeliveries，在加载完成后调用afterLoadDeliveries
	const wrappedLoadDeliveries = async (forceLoad = false) => {
		// 先从本地存储加载状态
		loadStateFromStorage();

		// 加载派单数据
		await loadDeliveries(forceLoad);

		// 重新计算手动合并的派单
		afterLoadDeliveries();
	};

	return {
		getDeliveries,
		getDeliveryState,
		getDeliveryTasks,
		getTaskImages,
		hasData,
		loading,
		refreshing,
		selectedDeliveryIds,
		manualMergedDelivery,
		filterSettings,
		dataVersion, // 导出 dataVersion 以便调试
		getDeliveryName,
		loadDeliveries: wrappedLoadDeliveries,
		refreshDeliveries,
		setSelectedDeliveryIds,
		getSelectedDeliveryIds,
		setManualMergedDelivery,
		getManualMergedDelivery,
		setFilterSettings,
		getFilterSettings,
		getFilteredDeliveries,
		mergeTodayDeliveries,
		loadStateFromStorage,
		saveStateToStorage,
		saveToCache,
		loadFromCache
	};
});
