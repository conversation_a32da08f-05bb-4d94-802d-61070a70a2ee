import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import type {
	Delivery,
	Task,
	TaskImages,
	DeliveryState,
	TaskState,
	UploadState,
	MobileResult
} from '@/types';
import { ClientStore } from '@/types/ClientStore';
import { MobileApi } from '@/utils/api';
import { useAuthStore, useSettingsStore } from '@/stores';
import { getDateRange, formatDate, showToast } from '@/utils';

// 声明uni全局对象
declare const uni: any;

export const useDataStore = defineStore('data', () => {
	// ===== 核心数据存储 =====
	const clientStore = ref<ClientStore>({
		deliveryMap: new Map<string, Delivery>(),
		taskMap: new Map<string, Task>(),
		imageMap: new Map<string, TaskImages>(),
		uploadMap: new Map<string, TaskImages>(),
	});

	// ===== 状态管理 =====
	const deliveryStates = ref<Map<string, DeliveryState>>(new Map());
	const taskStates = ref<Map<string, TaskState>>(new Map());
	const uploadState = ref<UploadState>({
		uploadingCount: 0,
		uploadedCount: 0,
		uploadFailedCount: 0,
		uploadingQueue: [],
	});

	// ===== 基础状态 =====
	const hasData = ref(false);
	const loading = ref(false);
	const refreshing = ref(false);
	const dataVersion = ref(0); // 数据版本号，用于触发响应式更新

	// ===== 缓存配置 =====
	const CACHE_KEY = 'data_cache';

	// ===== 计算属性 =====
	// 获取所有派单
	const getDeliveries = computed(() => {
		dataVersion.value; // 建立响应式依赖
		const deliveryList = Array.from(clientStore.value.deliveryMap.values());
		console.log(`📋 [DATA] getDeliveries计算 - 派单数量: ${deliveryList.length}, dataVersion: ${dataVersion.value}`);
		return deliveryList;
	});

	// 获取所有任务
	const getTasks = computed(() => {
		dataVersion.value; // 建立响应式依赖
		const taskList = Array.from(clientStore.value.taskMap.values());
		console.log(`📋 [DATA] getTasks计算 - 任务数量: ${taskList.length}, dataVersion: ${dataVersion.value}`);
		return taskList;
	});

	// 获取所有服务端图片
	const getServerImages = computed(() => {
		dataVersion.value; // 建立响应式依赖
		const imageList = Array.from(clientStore.value.imageMap.values());
		console.log(`📋 [DATA] getServerImages计算 - 图片数量: ${imageList.length}, dataVersion: ${dataVersion.value}`);
		return imageList;
	});

	// 获取所有本地图片
	const getLocalImages = computed(() => {
		dataVersion.value; // 建立响应式依赖
		const imageList = Array.from(clientStore.value.uploadMap.values());
		console.log(`📋 [DATA] getLocalImages计算 - 本地图片数量: ${imageList.length}, dataVersion: ${dataVersion.value}`);
		return imageList;
	});

	// ===== 状态获取方法 =====
	// 获取派单状态
	const getDeliveryState = computed(() => (deliveryId: string): DeliveryState => {
		return deliveryStates.value.get(deliveryId) || {
			totalCount: 0,
			completeCount: 0,
			uncompleteCount: 0,
			spotTotalCount: 0,
			spotCompletedCount: 0,
			spotUncompleteCount: 0,
			zoneTotalCount: 0,
			zoneCompletedCount: 0,
			zoneUncompleteCount: 0
		};
	});

	// 获取任务状态
	const getTaskState = computed(() => (taskId: string): TaskState => {
		return taskStates.value.get(taskId) || {
			shouldTake: 0,
			hasTaken: 0,
			hasUploaded: 0,
			pendingUpload: 0
		};
	});

	// ===== 初始化方法 =====
	// 初始化派单状态
	const initDeliveryStates = () => {
		const newStates = new Map<string, DeliveryState>();
		clientStore.value.deliveryMap.forEach((delivery, deliveryId) => {
			newStates.set(deliveryId, {
				totalCount: delivery.totalCount || 0,
				completeCount: delivery.completeCount || 0,
				uncompleteCount: delivery.totalCount - delivery.completeCount,
				spotTotalCount: delivery.spotTotalCount || 0,
				spotCompletedCount: delivery.spotCompletedCount || 0,
				spotUncompleteCount: delivery.spotTotalCount - delivery.spotCompletedCount,
				zoneTotalCount: delivery.zoneTotalCount || 0,
				zoneCompletedCount: delivery.zoneCompletedCount || 0,
				zoneUncompleteCount: delivery.zoneTotalCount - delivery.zoneCompletedCount,
			});
		});
		deliveryStates.value = newStates;
		console.log(`📋 [DATA] 初始化了 ${newStates.size} 个派单状态`);
	};

	// 初始化任务状态
	const initTaskStates = () => {
		const newStates = new Map<string, TaskState>();
		clientStore.value.taskMap.forEach((task, taskId) => {
			// 计算本地待上传图片数量
			let pendingUploadCount = 0;
			for (const [imageId, image] of clientStore.value.uploadMap) {
				if (image.taskId === taskId && image.imageStatus === 'pending') {
					pendingUploadCount++;
				}
			}

			newStates.set(taskId, {
				shouldTake: task.photoMax || 0,
				hasTaken: task.hasTaken || 0,
				hasUploaded: 0, // 客户端临时状态，通常为0
				pendingUpload: pendingUploadCount
			});
		});
		taskStates.value = newStates;
		console.log(`📋 [DATA] 初始化了 ${newStates.size} 个任务状态`);
	};

	// 初始化上传状态（从本地队列数据加载）
	const initUploadState = () => {
		// 首先从本地存储加载待上传图片数据
		try {
			const storedImages = uni.getStorageSync('localTaskImages');
			if (storedImages) {
				const localImages = JSON.parse(storedImages);

				// 将本地图片数据加载到uploadMap
				localImages.forEach((image: TaskImages) => {
					if (image.id && image.imageStatus === 'pending') {
						clientStore.value.uploadMap.set(image.id, image);
					}
				});

				console.log(`📋 [DATA] 从本地存储加载了 ${localImages.length} 张图片，其中 ${clientStore.value.uploadMap.size} 张待上传`);
			}
		} catch (error) {
			console.warn('📋 [DATA] 加载本地图片数据失败:', error);
		}

		// 统计各种状态的图片数量
		let pendingCount = 0,
			queueCount = 0,
			failedCount = 0;
		clientStore.value.uploadMap.forEach((image) => {
			if (image.imageStatus === 'pending') {
				pendingCount++;
			} else if (image.imageStatus === 'uploading') {
				queueCount++;
			} else if (image.imageStatus === 'failed') {
				failedCount++;
			}
		});

		uploadState.value = {
			uploadingCount: queueCount,
			uploadedCount: 0, // 这个值会在上传成功后更新
			uploadFailedCount: failedCount,
			uploadingQueue: [], // 这个队列会在上传过程中动态管理
		};

		console.log(`📋 [DATA] 初始化上传状态: 待传${pendingCount}张, 上传中${queueCount}张, 失败${failedCount}张`);
	};

	// ===== 缓存管理 =====
	// 保存到缓存
	const saveToCache = () => {
		if (clientStore.value.deliveryMap.size === 0) return;
		try {
			const cacheData = {
				deliveries: Array.from(clientStore.value.deliveryMap.values()),
				tasks: Array.from(clientStore.value.taskMap.values()),
				images: Array.from(clientStore.value.imageMap.values()),
				uploadMap: Array.from(clientStore.value.uploadMap.values()),
				deliveryStates: Object.fromEntries(deliveryStates.value),
				taskStates: Object.fromEntries(taskStates.value),
			};

			uni.setStorageSync(CACHE_KEY, cacheData);
			console.info('📋 [DATA] 数据已缓存');
		} catch (error) {
			console.error('❌ [DATA] 缓存数据失败:', error);
		}
	};

	// 从缓存加载
	const loadFromCache = () => {
		try {
			const cache = uni.getStorageSync(CACHE_KEY);
			if (cache) {
				// 清空现有数据
				clientStore.value.deliveryMap.clear();
				clientStore.value.taskMap.clear();
				clientStore.value.imageMap.clear();
				clientStore.value.uploadMap.clear();

				// 恢复数据
				cache.deliveries?.forEach((delivery: Delivery) => {
					if (delivery.deliveryId) {
						clientStore.value.deliveryMap.set(delivery.deliveryId, delivery);
					}
				});
				cache.tasks?.forEach((task: Task) => {
					if (task.taskId) {
						clientStore.value.taskMap.set(task.taskId, task);
					}
				});
				cache.images?.forEach((image: TaskImages) => {
					if (image.id) {
						clientStore.value.imageMap.set(image.id, image);
					}
				});
				cache.uploadMap?.forEach((image: TaskImages) => {
					if (image.id) {
						clientStore.value.uploadMap.set(image.id, image);
					}
				});

				// 初始化状态
				initDeliveryStates();
				initTaskStates();
				initUploadState();

				hasData.value = true;
				// 触发响应式更新
				dataVersion.value++;
				console.info('📋 [DATA] 从缓存加载了数据');
				console.info(`  - 派单: ${clientStore.value.deliveryMap.size} 条`);
				console.info(`  - 任务: ${clientStore.value.taskMap.size} 条`);
				console.info(`  - 服务端图片: ${clientStore.value.imageMap.size} 张`);
				console.info(`  - 本地图片: ${clientStore.value.uploadMap.size} 张`);
				return true;
			}
		} catch (error) {
			console.error('❌ [DATA] 加载缓存数据失败:', error);
		}
		return false;
	};

	// ===== 数据加载方法 =====
	// 加载数据
	const loadData = async (forceLoad = false) => {
		// 如果已有数据且不是强制加载，尝试从缓存加载
		if (hasData.value && !forceLoad) {
			if (loadFromCache()) return;
		}

		if (!refreshing.value) {
			loading.value = true;
		}

		try {
			const authStore = useAuthStore();
			const settingsStore = useSettingsStore();
			const username = authStore.getUsername();

			// 验证用户名是否有效
			if (!username || username.trim() === '') {
				console.error('❌ [DATA] 用户名为空，无法加载数据');
				showToast('用户信息无效，请重新登录', 'none');
				return;
			}

			// 根据设置获取时间范围
			const { startDay, endDay } = getDateRange(settingsStore.settings.orderPeriod);

			console.info(`📋 [DATA] 开始加载数据: 用户=${username}, 时间范围=${startDay} ~ ${endDay}`);

			const response = await MobileApi.loadMobileTasks(username, startDay, endDay);
			const mobileResult = response?.data as MobileResult;

			// 清空现有数据
			clientStore.value.deliveryMap.clear();
			clientStore.value.taskMap.clear();
			clientStore.value.imageMap.clear();

			// 更新静态数据
			mobileResult.deliveries?.forEach((delivery) => {
				if (delivery.deliveryId) {
					clientStore.value.deliveryMap.set(delivery.deliveryId, delivery);
				}
			});
			mobileResult.tasks?.forEach((task) => {
				if (task.taskId) {
					clientStore.value.taskMap.set(task.taskId, task);
				}
			});
			mobileResult.images?.forEach((image) => {
				if (image.id) {
					clientStore.value.imageMap.set(image.id, image);
				}
			});

			// 初始化状态
			initDeliveryStates();
			initTaskStates();
			initUploadState();

			hasData.value = true;
			// 触发响应式更新
			dataVersion.value++;
			saveToCache();

			console.info(`📋 [DATA] 加载完成:`);
			console.info(`  - 派单: ${mobileResult.deliveries?.length} 条`);
			console.info(`  - 任务: ${mobileResult.tasks?.length} 条`);
			console.info(`  - 图片: ${mobileResult.images?.length} 张`);
		} catch (error) {
			console.error('❌ [DATA] 加载数据失败:', error);
		} finally {
			loading.value = false;
		}
	};

	// 刷新数据
	const refreshData = async () => {
		try {
			refreshing.value = true;
			await loadData(true);
		} finally {
			refreshing.value = false;
		}
	};

	// ===== 数据操作方法 =====
	// 更新任务状态
	const updateTaskStatus = (taskId: string | number, newStatus: string) => {
		try {
			const task = clientStore.value.taskMap.get(taskId.toString());
			if (task) {
				const oldStatus = task.taskStatus;
				task.taskStatus = newStatus;

				// 更新 Map 中的任务
				clientStore.value.taskMap.set(taskId.toString(), task);

				// 保存到缓存
				saveToCache();

				// 触发响应式更新
				dataVersion.value++;

				console.log(`📋 [DATA] 任务 ${taskId} 状态更新成功: ${oldStatus} → ${newStatus}`);
				return true;
			} else {
				console.warn(`📋 [DATA] 未找到任务 ${taskId}`);
				return false;
			}
		} catch (error) {
			console.error(`📋 [DATA] 更新任务 ${taskId} 状态失败:`, error);
			return false;
		}
	};

	// 更新单个任务的状态（当图片发生变化时）
	const updateTaskStateForImage = (taskId: string) => {
		// 计算本地待上传图片数量
		let pendingUploadCount = 0;
		for (const [imageId, image] of clientStore.value.uploadMap) {
			if (image.taskId === taskId && image.imageStatus === 'pending') {
				pendingUploadCount++;
			}
		}

		// 获取任务信息
		const task = clientStore.value.taskMap.get(taskId);
		if (task) {
			const currentState = taskStates.value.get(taskId) || {
				shouldTake: 0,
				hasTaken: 0,
				hasUploaded: 0,
				pendingUpload: 0
			};

			// 更新状态
			const newState: TaskState = {
				shouldTake: task.photoMax || 0,
				hasTaken: task.hasTaken || 0,
				hasUploaded: 0, // 客户端临时状态，通常为0
				pendingUpload: pendingUploadCount
			};

			taskStates.value.set(taskId, newState);
			console.log(`📋 [DATA] 更新任务 ${taskId} 状态:`, newState);
		}
	};

	// ===== 图片管理方法 =====
	// 获取任务的服务端图片（已上传）
	const getTaskServerImages = (taskId: string | number) => {
		const images: TaskImages[] = [];
		for (const [imageId, image] of clientStore.value.imageMap) {
			if (image.taskId === taskId.toString()) {
				images.push(image);
			}
		}
		console.log(`📋 [DATA] 获取任务 ${taskId} 的服务端图片: ${images.length} 张`);
		return images;
	};

	// 获取任务的本地图片（待上传）
	const getTaskLocalImages = (taskId: string | number) => {
		const images: TaskImages[] = [];
		for (const [imageId, image] of clientStore.value.uploadMap) {
			if (image.taskId === taskId.toString()) {
				images.push(image);
			}
		}
		console.log(`📋 [DATA] 获取任务 ${taskId} 的本地图片: ${images.length} 张`);
		return images;
	};

	// 获取任务的所有图片（服务端 + 本地）
	const getTaskAllImages = (taskId: string | number) => {
		const serverImages = getTaskServerImages(taskId);
		const localImages = getTaskLocalImages(taskId);
		const allImages = [...serverImages, ...localImages];
		console.log(
			`📋 [DATA] 获取任务 ${taskId} 的所有图片: 服务端${serverImages.length}张 + 本地${localImages.length}张 = 总计${allImages.length}张`
		);
		return allImages;
	};

	// 添加服务端图片到imageMap
	const addServerImage = (image: TaskImages) => {
		if (!image.id) {
			console.warn('📋 [DATA] 服务端图片缺少ID，无法添加');
			return false;
		}

		clientStore.value.imageMap.set(image.id, image);
		console.log(`📋 [DATA] 添加服务端图片: ${image.id} (任务: ${image.taskId})`);

		// 保存到缓存并触发更新
		saveToCache();
		dataVersion.value++;
		return true;
	};

	// 添加本地图片到uploadMap
	const addLocalImage = (image: TaskImages) => {
		if (!image.id) {
			console.warn('📋 [DATA] 本地图片缺少ID，无法添加');
			return false;
		}

		if (!image.taskId) {
			console.warn('📋 [DATA] 本地图片缺少taskId，无法添加');
			return false;
		}

		// 确保图片状态为待上传
		image.imageStatus = 'pending';

		clientStore.value.uploadMap.set(image.id, image);
		console.log(`📋 [DATA] 添加本地图片: ${image.id} (任务: ${image.taskId})`);

		// 更新任务状态
		updateTaskStateForImage(image.taskId);

		// 保存到缓存并触发更新
		saveToCache();
		dataVersion.value++;

		// 发送图片添加事件
		uni.$emit('imageAdded', {
			taskId: image.taskId,
			imageId: image.id,
			timestamp: Date.now()
		});

		return true;
	};

	// 删除图片（从对应的Map中移除）
	const removeImage = (imageId: string, isLocal: boolean = false) => {
		const targetMap = isLocal ? clientStore.value.uploadMap : clientStore.value.imageMap;
		const mapName = isLocal ? 'uploadMap' : 'imageMap';

		if (targetMap.has(imageId)) {
			const image = targetMap.get(imageId);
			targetMap.delete(imageId);

			console.log(`📋 [DATA] 从${mapName}删除图片: ${imageId} (任务: ${image?.taskId})`);

			// 重新计算任务状态
			if (image?.taskId) {
				updateTaskStateForImage(image.taskId);
			}

			// 保存到缓存并触发更新
			saveToCache();
			dataVersion.value++;
			return true;
		} else {
			console.warn(`📋 [DATA] 在${mapName}中未找到图片: ${imageId}`);
			return false;
		}
	};

	// 删除本地图片
	const removeLocalImage = (imageId: string) => {
		return removeImage(imageId, true);
	};

	// 更新图片状态
	const updateImageStatus = (imageId: string, status: 'pending' | 'uploading' | 'complete' | 'failed') => {
		// 先在uploadMap中查找
		let image = clientStore.value.uploadMap.get(imageId);
		let isLocal = true;

		// 如果uploadMap中没有，在imageMap中查找
		if (!image) {
			image = clientStore.value.imageMap.get(imageId);
			isLocal = false;
		}

		if (image && image.taskId) {
			const oldStatus = image.imageStatus;
			image.imageStatus = status;

			// 更新对应的Map
			if (isLocal) {
				clientStore.value.uploadMap.set(imageId, image);
			} else {
				clientStore.value.imageMap.set(imageId, image);
			}

			console.log(`📋 [DATA] 更新图片状态: ${imageId} (${oldStatus} → ${status})`);

			// 更新任务状态
			updateTaskStateForImage(image.taskId);

			// 保存到缓存并触发更新
			saveToCache();
			dataVersion.value++;

			// 发送图片状态变化事件
			uni.$emit('imageStatusChanged', {
				taskId: image.taskId,
				imageId: imageId,
				oldStatus: oldStatus,
				newStatus: status,
				timestamp: Date.now()
			});

			return true;
		} else {
			console.warn(`📋 [DATA] 未找到图片或图片缺少taskId: ${imageId}`);
			return false;
		}
	};

	// 获取任务的图片统计信息
	const getTaskImageStats = (taskId: string, photoMax: number = 0) => {
		const taskState = getTaskState.value(taskId);

		return {
			shouldTake: photoMax || taskState.shouldTake, // 应拍（服务端）
			hasTaken: taskState.hasTaken, // 已拍（服务端已确认）
			hasUploaded: taskState.hasUploaded, // 已传（客户端临时，很快会被清空）
			pendingUpload: taskState.pendingUpload, // 待传（客户端本地）
		};
	};

	// 清理已上传的图片（服务器数据加载后调用）
	const cleanupUploadedImages = () => {
		const toRemove: string[] = [];

		// 找出状态为complete的图片
		for (const [imageId, image] of clientStore.value.uploadMap) {
			if (image.imageStatus === 'complete') {
				toRemove.push(imageId);
			}
		}

		// 删除这些图片
		toRemove.forEach(imageId => {
			clientStore.value.uploadMap.delete(imageId);
		});

		if (toRemove.length > 0) {
			console.log(`📋 [DATA] 清理了 ${toRemove.length} 张已上传的图片`);

			// 重新计算所有任务状态
			initTaskStates();

			// 保存到缓存并触发更新
			saveToCache();
			dataVersion.value++;
		}
	};

	// 将本地图片移动到服务端图片（上传成功后）
	const moveImageToServer = (imageId: string) => {
		const localImage = clientStore.value.uploadMap.get(imageId);
		if (localImage) {
			// 从uploadMap移除
			clientStore.value.uploadMap.delete(imageId);

			// 添加到imageMap
			clientStore.value.imageMap.set(imageId, localImage);

			console.log(`📋 [DATA] 图片上传成功，移动到服务端: ${imageId} (任务: ${localImage.taskId})`);

			// 保存到缓存并触发更新
			saveToCache();
			dataVersion.value++;
			return true;
		} else {
			console.warn(`📋 [DATA] 未找到本地图片: ${imageId}`);
			return false;
		}
	};

	return {
		// 数据存储
		clientStore,

		// 状态管理
		deliveryStates,
		taskStates,
		uploadState,

		// 基础状态
		hasData,
		loading,
		refreshing,
		dataVersion,

		// 计算属性
		getDeliveries,
		getTasks,
		getServerImages,
		getLocalImages,
		getDeliveryState,
		getTaskState,

		// 初始化方法
		initDeliveryStates,
		initTaskStates,
		initUploadState,

		// 缓存管理
		saveToCache,
		loadFromCache,

		// 数据加载
		loadData,
		refreshData,

		// 数据操作
		updateTaskStatus,
		updateTaskStateForImage,

		// 图片管理方法
		getTaskServerImages,
		getTaskLocalImages,
		getTaskAllImages,
		addServerImage,
		addLocalImage,
		removeLocalImage,
		updateImageStatus,
		getTaskImageStats,
		cleanupUploadedImages,
		moveImageToServer,
		removeImage,
	};
});
