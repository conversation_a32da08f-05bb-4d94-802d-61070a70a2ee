import { OSS_URL } from './env';
import { HttpClient } from './request';

// 声明类型，避免导入错误
interface LoginCredentials {
	username : string;
	password : string;
}

interface AuthResponse {
	access_token : string;
	token_type : string;
	refresh_token : string;
	expires_in : number;
	scope : string;
	[key : string] : any;
}

interface UserInfo {
	username : string;
	name : string;
	[key : string] : any;
}

/**
 * 认证相关API
 */
export class AuthApi {
	/**
	 * 用户登录
	 * @param credentials 登录凭证
	 * @returns 认证响应
	 */
	static async login(credentials : LoginCredentials) : Promise<AuthResponse> {
		// 验证登录凭证
		if (!credentials.username || !credentials.password) {
			throw new Error('用户名和密码不能为空');
		}

		console.info(`🔐 [API] 发送登录请求: ${credentials.username}`);

		return await HttpClient.post<AuthResponse>(
			'/oauth2/token',
			{
				username: credentials.username,
				password: credentials.password,
				grant_type: 'password',
				scope: 'openid',
			},
			{
				header: {
					'Content-Type': 'application/x-www-form-urlencoded',
				},
			}
		);
	}

	/**
	 * 获取用户信息
	 * @returns 用户信息
	 */
	static async getUserInfo() : Promise<UserInfo> {
		return await HttpClient.get<UserInfo>('/userinfo');
	}

	/**
	 * 登出
	 * @param accessToken 访问令牌
	 */
	static async logout(accessToken : string) : Promise<void> {
		await HttpClient.put<void>(
			'/oauth2/sign-out',
			{
				accessToken,
			},
			{
				header: {
					'Content-Type': 'application/x-www-form-urlencoded',
				},
			}
		);
	}
}
export class MobileApi {
	/**
	 * 加载手机端任务，包括了所有客户端所需要的数据
	 * @param username 用户名
	 * @param startDay 开始日期 (可选)
	 * @param endDay 结束日期 (可选)
	 * @returns 派单列表
	 */
	static async loadMobileTasks(username : string, startDay ?: string, endDay ?: string) : Promise<any> {
		// 验证必要参数
		if (!username || username.trim() === '') {
			throw new Error('用户名不能为空');
		}

		console.info(`📋 [API] 加载用户派单: 用户=${username}, 时间范围=${startDay || '无'} ~ ${endDay || '无'}`);

		const requestData : any = {
			username,
		};

		// 如果提供了时间范围参数，则添加到请求中
		if (startDay) {
			requestData.startDay = startDay;
		}
		if (endDay) {
			requestData.endDay = endDay;
		}

		return await HttpClient.post('/open/v1/mobile/load-mobile-tasks', requestData);
	}
	
	
	/**
	 * 上传拍照文件
	 * @param filePath 文件路径
	 * @param formData 表单数据
	 * @param onProgressUpdate 进度更新回调
	 * @returns 上传结果
	 */
	static async uploadImage(
		filePath : string,
		formData : Record<string, any>,
		onProgressUpdate ?: (res : any) => void
	) : Promise<any> {
		return await HttpClient.uploadFile({
			url: '/open/v2/mobile/upload-image',
			filePath,
			name: 'file',
			formData,
			onProgressUpdate,
		});
	}
	
}





/**
 * 文件上传相关API
 */
export class OssApi {
	static getOssImageUrl(url : string) : string {
		if (url && (url.startsWith('omaps/') || url.startsWith('images/') || url.startsWith('shared/')))
			return OSS_URL + '/' + url;

		return url;
	}
}