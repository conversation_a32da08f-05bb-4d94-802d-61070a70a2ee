<template>
	<view class="task-container" :style="dynamicFontStyle">
		<!-- 任务导航栏 -->
		<task-navbar
			:total-count="totalCount"
			:current-count="currentCount"
			:delivery-name="selectedDeliveryName"
			:delivery-count="selectedDeliveryIds.length"
			@search="onSearchClick"
			@config="onConfigClick"
			@back="onBackToDelivery" />

		<!-- 筛选选项卡 -->
		<task-filter
			:district-names="districtNames"
			:selected-district-name="selectedDistrictName"
			:selected-district-index="selectedDistrictIndex"
			:zone-names="zoneNames"
			:selected-zone-name="selectedZoneName"
			:selected-zone-index="selectedZoneIndex"
			:zone-details="zoneDetails"
			:district-details="districtDetails"
			@district-change="onDistrictChange"
			@zone-change="onZoneChange"
			@clear-district="clearDistrictFilter"
			@clear-zone="clearZoneFilter"
			@filter-change="handleFilterTabChange" />
		<scroll-view scroll-y class="task-list">
			<!-- 当没有选择派单时显示提示 -->
			<view v-if="selectedDeliveryIds.length === 0" class="empty-state">
				<view class="empty-icon">
					<text class="iconfont icon-clipboard"></text>
				</view>
				<text class="empty-title">请先选择派单</text>
				<text class="empty-desc">请前往派单页面选择一个或多个派单</text>
			</view>
			<!-- 当选择了派单但没有任务时显示提示 -->
			<view v-else-if="filteredTasks.length === 0" class="empty-state">
				<text>暂无任务信息</text>
			</view>
			<view v-else class="task-items">
				<view v-for="task in filteredTasks" :key="task.taskId" class="task-item" @click="handleTaskClick(task)">
					<!-- 第一行：图片和任务信息 -->
					<view class="task-content-row">
						<!-- 所有任务都显示图片 -->
						<view class="task-images">
							<view class="task-image">
								<smart-image
									v-if="task.previousContentImageUrl"
									:src="task.previousContentImageUrl"
									:placeholder-text="'查看历史图片'"
									:image-style="'width: 130rpx; height: 130rpx; border-radius: 8rpx; object-fit: cover;'"
									mode="aspectFill"
									@load="handleImageLoad('previous', task.taskId)"
									@error="handleImageError('previous', task.taskId)"
									@click="previewImage(task.previousContentImageUrl)"
								/>
								<view v-else class="default-image-placeholder" @click.stop>
									<text class="default-image-text iconfont icon-album"></text>
								</view>
							</view>
							<view class="task-image">
								<smart-image
									v-if="task.currentContentImageUrl"
									:src="task.currentContentImageUrl"
									:placeholder-text="'查看当前图片'"
									:image-style="'width: 130rpx; height: 130rpx; border-radius: 8rpx; object-fit: cover;'"
									mode="aspectFill"
									@load="handleImageLoad('current', task.taskId)"
									@error="handleImageError('current', task.taskId)"
									@click="previewImage(task.currentContentImageUrl)"
								/>
								<view v-else class="default-image-placeholder" @click.stop>
									<text class="default-image-text iconfont icon-album"></text>
								</view>
							</view>
						</view>
						<view class="task-info">
							<view class="task-header">
								<view class="task-id-container">
									<view class="task-id-wrapper">
										<!-- 任务类型标识和任务ID在同一行 -->
										<view class="task-id-row">
											<view
												class="task-type-badge"
												:class="{
													'zone-task': task.spotId === null,
													'spot-task': task.spotId !== null,
												}">
												<text
													class="task-type-icon iconfont"
													:class="task.spotId === null ? 'icon-house' : 'icon-point'"></text>
												<text class="task-type-text">{{
													task.spotId === null ? '实景' : '点位'
												}}</text>
											</view>
											<text class="task-id" v-if="task.spotId !== null"
												>{{ task.spotCode }} ({{ task.belongDistrict }})</text
											>
											<text class="task-id" v-else
												>{{ task.zoneName }} ({{ task.belongDistrict }})</text
											>
											<!-- 显示折叠任务数量 -->
											<text
												v-if="task.collapsedZoneTasks && task.collapsedZoneTasks.length > 0"
												class="merged-count">
												(合并{{ task.collapsedZoneTasks.length + 1 }} 个任务)
											</text>
										</view>
									</view>
								</view>
							</view>
							<view class="info-row zone-name-row">
								<text class="label">楼盘：</text>
								<text class="value">{{ task?.zoneName || '未设置' }}</text>
								<text class="iconfont icon-navigation" @click.stop="openLocation(task)"></text>
							</view>
							<view class="info-row">
								<text class="label">{{ task.zoneTask ? '位置：' : '位置：' }}</text>
								<text class="value">{{ task ? getShotLocation(task) : '未设置' }}</text>
							</view>
							<view class="info-row">
								<text class="label">地址：</text>
								<text class="value">{{ task?.zoneAddress || '未设置' }}</text>
							</view>
							<view class="info-row">
								<text class="label">上刊：</text>
								<text class="value"
									>{{ formatDate(task?.beginDay) || '未设置' }} 至
									{{ formatDate(task?.endDay) || '未设置' }}</text
								>
							</view>
							<view class="info-row delivery-row">
								<text class="label">派单：</text>
								<view class="value-container">
									<!-- 如果是数组，分行显示每个派单 -->
									<template v-if="Array.isArray(getTaskDeliveryName(task))">
										<view
											v-for="(deliveryName, index) in getTaskDeliveryName(task)"
											:key="index"
											class="delivery-item">
											{{ deliveryName }}
										</view>
									</template>
									<!-- 如果不是数组，直接显示 -->
									<text v-else class="value">
										{{ getTaskDeliveryName(task) }}
									</text>
								</view>
							</view>
						</view>
					</view>
					<!-- 第二行：照片统计信息 - 独占一行，使用整个任务项宽度，居中对齐 -->
					<view class="task-photo-stats">
						<text class="count-item required highlight-stats">
							<text class="stats-label-highlight">应拍：</text>
							<text class="stats-value-highlight">{{ getTaskStatsReactive(task).shouldTake }}</text>
						</text>
						<text class="count-item uploaded highlight-stats">
							<text
								class="stats-label-highlight"
								:class="{
									'incomplete-red':
										getTaskStatsReactive(task).hasTaken + getTaskStatsReactive(task).pendingUpload <
										getTaskStatsReactive(task).shouldTake,
								}"
								>已传：</text
							>
							<text
								class="stats-value-highlight"
								:class="{
									'incomplete-red':
										getTaskStatsReactive(task).hasTaken + getTaskStatsReactive(task).pendingUpload <
										getTaskStatsReactive(task).shouldTake,
								}"
								>{{ getTaskStatsReactive(task).hasTaken }}</text
							>
						</text>
						<text
							class="count-item pending highlight-stats"
							:class="{
								'zero-pending': getTaskStatsReactive(task).pendingUpload === 0,
							}">
							<text
								class="stats-label-highlight"
								:class="{
									'zero-pending-label': getTaskStatsReactive(task).pendingUpload === 0,
								}"
								>待传：</text
							>
							<text
								class="stats-value-highlight"
								:class="{
									pending:
										getTaskStatsReactive(task).pendingUpload > 0 &&
										getTaskStatsReactive(task).hasTaken < getTaskStatsReactive(task).shouldTake,
									completed:
										getTaskStatsReactive(task).pendingUpload === 0 ||
										getTaskStatsReactive(task).hasTaken >= getTaskStatsReactive(task).shouldTake,
									'zero-pending-value': getTaskStatsReactive(task).pendingUpload === 0,
								}"
								>{{ getTaskStatsReactive(task).pendingUpload }}</text
							>
						</text>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed, reactive } from 'vue';
import { storeToRefs } from 'pinia';
import { onShow } from '@dcloudio/uni-app';
import { useTaskStore, useImagesStore, useDeliveryStore } from '@/stores';
import { formatDate, showToast } from '../../utils';
import { TaskUtils } from '../../utils/task';
import TaskNavbar from './components/task-navbar.vue';
import TaskFilter from './components/task-filter.vue';
import { useSettingsStore } from '../../stores/settings';
import { ImageDisplayUtils } from '../../utils/image-display';
import SmartImage from '../../components/smart-image.vue';

// 声明全局 uni 对象，解决 TypeScript 错误
declare const uni: any;
declare const plus: any;

// 从本地存储加载筛选状态
const loadFilterState = () => {
	try {
		const photoStatus = uni.getStorageSync('task_photo_status_filter');
		const completionStatus = uni.getStorageSync('task_completion_status_filter');
		return {
			photoStatus: photoStatus || '',
			completionStatus: completionStatus || '',
		};
	} catch (error) {
		console.warn('加载筛选状态失败:', error);
		return { photoStatus: '', completionStatus: '' };
	}
};

// 保存筛选状态到本地存储
const saveFilterState = (photoStatus: string, completionStatus: string) => {
	try {
		uni.setStorageSync('task_photo_status_filter', photoStatus);
		uni.setStorageSync('task_completion_status_filter', completionStatus);
	} catch (error) {
		console.warn('保存筛选状态失败:', error);
	}
};

// 使用设置 store
const settingsStore = useSettingsStore();

// 加载筛选状态
const filterState = loadFilterState();

// 任务类型筛选
const taskTypeFilter = ref(settingsStore.settings.taskDisplaySettings.taskType || 'all');
// 拍照状态筛选
const photoStatusFilter = ref(filterState.photoStatus);
// 完成状态筛选
const completionStatusFilter = ref(filterState.completionStatus);

// 搜索文本
const searchText = ref('');

const taskStore = useTaskStore();
const { tasks, selectedDeliveryIds, currentTasks, filteredTasks } = storeToRefs(taskStore);
const { loadTasks, getTaskDeliveryName, getShotLocation, setFilters } = taskStore;

const deliveryStore = useDeliveryStore();
const { deliveries } = storeToRefs(deliveryStore);

const { loadDeliveries, getDeliveryName } = deliveryStore;

const selectedDeliveryName = ref('');
const selectedZoneName = ref('');
const selectedZoneId = ref(0);

// 获取小区索引
const selectedZoneIndex = computed(() => {
	return Array.from(zoneMap.value.values()).findIndex((option) => option.id === selectedZoneId.value);
});

// 使用Map优化小区数据结构
const zoneMap = computed(() => {
	const map = new Map();
	filteredTasks.value.forEach((task) => {
		if (task.zoneId && task.zoneName) {
			map.set(task.zoneId, {
				id: task.zoneId,
				name: task.zoneName,
				address: task.zoneAddress,
				district: task.belongDistrict,
			});
		}
	});
	return map;
});

// 计算小区名称列表（用于picker显示）
const zoneNames = computed(() => Array.from(zoneMap.value.values()).map((item) => item.name));

// 计算小区详细信息（包括地址和任务统计）- 基于所有任务（tasks）
const zoneDetails = computed(() => {
	return Array.from(zoneMap.value.values()).map((zone: any) => {
		// 获取该小区的任务（基于所有任务，显示真实的小区任务总数）
		const zoneTasks = tasks.value.filter((task: any) => task.zoneId === zone.id);

		// 获取小区所属区域
		const belongDistrict = zoneTasks.length > 0 ? zoneTasks[0].belongDistrict : '';

		// 统计任务状态（基于所有任务）
		const totalTasks = zoneTasks.length; // 总任务数
		const completedTasks = zoneTasks.filter((task: any) => task.taskStatus === 'COMPLETED').length; // 已完成任务数
		const incompleteTasks = totalTasks - completedTasks; // 未完成任务数 = 总-完

		// 待上传任务数：有图片需要上传的任务汇总
		const pendingUploadTasks = zoneTasks.filter((task: any) => {
			const stats = getTaskStatsReactive(task);
			return stats.pendingUpload > 0; // 有待上传图片的任务
		}).length;

		return {
			zoneName: zone.name,
			zoneAddress: zone.address || `${zone.name}`,
			belongDistrict, // 添加所属区域信息
			totalTasks, // 总任务数
			completedTasks, // 已完成任务数
			incompleteTasks, // 未完成任务数
			pendingUploadTasks, // 待上传任务数
		};
	});
});

// 计算总任务数量（基于 currentTasks）
const totalCount = computed(() => {
	return currentTasks.value.length;
});

// 计算当前显示的任务数量（基于 filteredTasks）
const currentCount = computed(() => {
	return filteredTasks.value.length;
});

// 统一的过滤器处理逻辑
const handleFilterChange = (type: string, e: any) => {
	const index = Number(e.detail.value);
	switch (type) {
		case 'zone':
			const zoneItem = Array.from(zoneMap.value.values())[index];
			if (zoneItem) {
				selectedZoneId.value = zoneItem.id;
				selectedZoneName.value = zoneItem.name;
				// 更新筛选条件
				setFilters({ zone: zoneItem.id });
			}
			break;
		case 'district':
			const district = districtNames.value[index];
			if (district) {
				selectedDistrictName.value = district;
				selectedDistrictIndex.value = index;
				// 更新筛选条件
				setFilters({ district });
			}
			break;
	}
};

// 统一的清除过滤器逻辑
const clearFilter = (type: string) => {
	switch (type) {
		case 'zone':
			selectedZoneId.value = 0;
			selectedZoneName.value = '';
			// 更新筛选条件
			setFilters({ zone: 0 });
			break;
		case 'district':
			selectedDistrictName.value = '';
			selectedDistrictIndex.value = 0;
			// 更新筛选条件
			setFilters({ district: '' });
			break;
	}
};

// 绑定处理函数
const onZoneChange = (e: any) => handleFilterChange('zone', e);
const onDistrictChange = (e: any) => handleFilterChange('district', e);

// 绑定清除函数
const clearZoneFilter = () => clearFilter('zone');
const clearDistrictFilter = () => clearFilter('district');
const imagesStore = useImagesStore();
const { addLocalImage, getTaskStats, getLocalTaskImages } = imagesStore;

// 强制更新计算属性的触发器
const forceUpdateTrigger = ref(0);

// 创建响应式的任务统计计算属性
const taskStatsCache = computed(() => {
	const cache = new Map();
	// 确保依赖 localImages、filteredTasks、taskStore.tasks 和强制更新触发器
	const localImages = imagesStore.localImages; // 触发对 localImages 的依赖
	const tasks = filteredTasks.value;
	const allTasks = taskStore.tasks; // 触发对 taskStore.tasks 的依赖
	const trigger = forceUpdateTrigger.value; // 触发强制更新依赖

	// 强制读取数据以建立响应式依赖
	const imageCount = localImages.length;
	const taskCount = allTasks.length;

	// 计算所有任务的 taskImages 总数，强制建立对嵌套属性的依赖
	const totalServerImages = allTasks.reduce((total, task) => {
		return total + (task.taskImages?.length || 0);
	}, 0);

	console.info(
		`📊 [TASK] 重新计算任务统计缓存，本地图片: ${imageCount}, 任务数: ${taskCount}, 服务端图片: ${totalServerImages}, 触发器: ${trigger}`
	);

	tasks.forEach((task: any) => {
		const stats = getTaskStats(task.taskId, task.photoMax || 0);
		cache.set(task.taskId, stats);
	});

	return cache;
});

// 创建响应式的获取函数
const getTaskStatsReactive = (task: any) => {
	return (
		taskStatsCache.value.get(task.taskId) || {
			shouldTake: 0,
			hasTaken: 0,
			hasUploaded: 0,
			pendingUpload: 0,
		}
	);
};

// 注意：图片缓存检查和智能显示逻辑现在由 smart-image 组件内部处理

// 字体大小管理
const currentFontScale = ref(1);

// 动态字体样式
const dynamicFontStyle = computed(() => ({
	'--font-scale': currentFontScale.value,
}));

// 初始化字体大小
const initFontSize = () => {
	try {
		const savedFontScale = uni.getStorageSync('fontScale') || 1;
		currentFontScale.value = savedFontScale;
		console.info(`📄 [TASK] 页面字体大小初始化: 缩放 ${savedFontScale}`);
	} catch (error) {
		console.error('页面字体大小初始化失败:', error);
	}
};

// 处理字体大小变化
const handleFontSizeChange = (data: any) => {
	const { size, scale } = data;
	currentFontScale.value = scale;
	console.info(`📄 [TASK] 页面字体大小已更新: ${size} (缩放: ${scale})`);
};

// 图片加载状态管理
const imageLoading = reactive({
	previous: {},
	current: {},
});

const imageError = reactive({
	previous: {},
	current: {},
});

// 处理图片加载完成
const handleImageLoad = (type: 'previous' | 'current', taskId: string) => {
	imageLoading[type][taskId] = false;
	imageError[type][taskId] = false;
};

// 处理图片加载错误
const handleImageError = (type: 'previous' | 'current', taskId: string) => {
	imageLoading[type][taskId] = false;
	imageError[type][taskId] = true;
};

// 预览图片
const previewImage = (imageUrl: string) => {
	if (!imageUrl) {
		showToast('图片不存在');
		return;
	}

	uni.previewImage({
		urls: [imageUrl],
		current: imageUrl,
		fail: (error) => {
			console.error('预览图片失败:', error);
			showToast('预览图片失败');
		},
	});
};

// 处理任务点击事件
const handleTaskClick = async (task: any) => {
	// 根据用户设置的任务模式决定行为
	const taskMode = settingsStore.settings.taskMode;

	if (taskMode === 'quick') {
		// 快拍模式：直接拍照
		await handleQuickPhotoMode(task);
	} else {
		// 普通模式：打开任务详情页面
		openTaskDetailPage(task);
	}
};

// 快拍模式处理函数
const handleQuickPhotoMode = async (task: any) => {
	const taskImages = getLocalTaskImages(task.taskId);
	const currentPhotoCount = taskImages.length;
	const maxPhotoCount = task?.photoMax || 0;

	if (currentPhotoCount >= maxPhotoCount) {
		showToast('已达到最大拍照数量限制');
		return;
	}

	try {
		// 根据任务的onlyPhoto属性决定拍照方式
		// 在快拍模式下，如果onlyPhoto为true，直接拍照；否则弹出选择框
		let imageUrl: string;

		if (task.onlyPhoto === true) {
			// 只允许拍照
			imageUrl = await TaskUtils.takePhoto();
		} else {
			// 允许从相册选择
			imageUrl = await TaskUtils.chooseImageSource(task);
		}

		// 获取位置信息
		let location:
			| {
					/// <reference path="./index.vue.1.__VLS_template.tsx" />
					latitude: number;
					longitude: number;
			  }
			| undefined;
		try {
			location = await TaskUtils.getLocation();
		} catch (error) {
			console.error('获取位置信息失败:', error);
			// 位置获取失败时不影响拍照功能继续进行
			location = undefined;
		}

		// 创建并保存任务图片
		const newTaskImage = TaskUtils.createTaskImage({
			zoneId: task.zoneId,
			taskId: task.taskId,
			spotId: task.spotId,
			spotCode: task.spotCode,
			imageUrl,
			location,
			task,
		});

		addLocalImage(task, newTaskImage);
		showToast('照片已保存到本地', 'success');
	} catch (error) {
		console.error('拍照失败:', error);
		showToast('拍照失败');
	}
};

// 打开任务详情页面
const openTaskDetailPage = (task: any) => {
	// 准备传递的基本数据
	const detailData: any = {
		task: task,
	};

	uni.navigateTo({
		url: '/pages/task/task-detail',
		events: {
			// 可以监听任务详情页面返回的事件
		},
		success: (res) => {
			// 向打开的页面发送任务数据
			if (res.eventChannel && typeof res.eventChannel.emit === 'function') {
				res.eventChannel.emit('taskDetail', detailData);
			}
		},
	});
};

// 打开地图导航
const openLocation = (task: any) => {
	if (!task.zoneAddress || !task.latitude || !task.longitude) {
		showToast('地址或位置信息未设置', 'none');
		return;
	}

	// 获取系统信息
	uni.getSystemInfo({
		success: (res) => {
			let url = '';
			const latitude = Number(task.latitude);
			const longitude = Number(task.longitude);
			// const name = encodeURIComponent(task.zoneName || '');
			const address = encodeURIComponent(task.zoneAddress);

			if (res.platform === 'ios') {
				// iOS使用Apple Maps
				url = `maps://maps.apple.com/?q=${address}&ll=${latitude},${longitude}`;
			} else if (res.platform === 'android') {
				// Android使用高德地图
				url = `androidamap://viewMap?sourceApplication=omaps&poiname=${address}&lat=${latitude}&lon=${longitude}&dev=0`;
			} else {
				// 其他平台使用通用地图URL
				url = `https://uri.amap.com/marker?position=${longitude},${latitude}&name=${address}&src=omaps&coordinate=gaode`;
			}

			// 使用系统浏览器打开URL
			plus.runtime.openURL(url, (err) => {
				if (err) {
					console.error('导航打开失败:', err);
					showToast('导航打开失败', 'none');
				}
			});
		},
		fail: (err) => {
			console.error('获取系统信息失败:', err);
			showToast('导航打开失败', 'none');
		},
	});
};

// 处理筛选选项卡变更
const handleFilterTabChange = (data: { tabIndex: number; value: string }) => {
	// 根据不同的选项卡和值进行处理
	switch (data.tabIndex) {
		case 1: // 项目名
			// 处理项目名排序
			break;
		case 2: // 广告位
			// 处理广告位筛选
			break;
		case 3: // 自定义
			// 处理自定义筛选
			let photoStatus = '';
			let completionStatus = '';

			if (data.value === 'not_taken') {
				photoStatus = 'notTaken';
			} else if (data.value === 'taken') {
				photoStatus = 'taken';
			} else if (data.value === 'completed') {
				completionStatus = 'complete';
			} else if (data.value === 'not_completed') {
				completionStatus = 'notComplete';
			}

			// 更新本地状态
			photoStatusFilter.value = photoStatus;
			completionStatusFilter.value = completionStatus;

			// 更新筛选条件
			setFilters({ photoStatus, completionStatus });
			break;
	}
};

// 处理搜索
const onSearchClick = (text: string) => {
	searchText.value = text;
	// 更新筛选条件
	setFilters({ searchText: text });
};

// 返回派单页面
const onBackToDelivery = () => {
	uni.switchTab({
		url: '/pages/delivery/index',
	});
};

// 处理设置点击
const onConfigClick = () => {
	// 直接显示任务显示设置对话框
	showDisplaySettings();
};

// 是否合并实景任务
const mergeZoneTasks = ref(settingsStore.settings.taskDisplaySettings.mergeZoneTasks);

// 任务排列方式
const taskSortMode = ref(settingsStore.settings.taskDisplaySettings.taskSortMode);

// 显示任务显示设置对话框
const showDisplaySettings = () => {
	// 定义事件处理函数
	const handleSettingsResult = (data: any) => {
		// 处理设置结果
		if (data.clearAll) {
			// 清空所有筛选条件
			clearAllFilters();
		} else {
			if (data.taskType) {
				// 设置任务类型筛选
				setTaskTypeFilter(data.taskType);
				// 更新筛选条件
				setFilters({ taskType: data.taskType });
			}
			if (data.photoStatus) {
				// 设置拍照状态筛选
				setPhotoStatusFilter(data.photoStatus);
				// 更新筛选条件
				setFilters({ photoStatus: data.photoStatus });
			}
			if (data.completionStatus) {
				// 设置完成状态筛选
				setCompletionStatusFilter(data.completionStatus);
				// 更新筛选条件
				setFilters({ completionStatus: data.completionStatus });
			}
			if (data.mergeZoneTasks !== undefined) {
				// 设置是否合并实景任务
				mergeZoneTasks.value = data.mergeZoneTasks;
			}
			if (data.taskSortMode) {
				// 设置任务排列方式
				taskSortMode.value = data.taskSortMode;
			}

			// 更新 settings store 中的任务显示设置已经在 task-settings.vue 中保存了
			// currentTasks 和 filteredTasks 会自动重新计算（computed）
		}
	};

	// 打开设置页面
	uni.navigateTo({
		url: '/pages/task/task-settings',
		events: {
			// 监听设置结果
			settingsResult: handleSettingsResult,
		},
		success: (res) => {
			try {
				// 向打开的页面发送当前设置
				if (res.eventChannel && typeof res.eventChannel.emit === 'function') {
					res.eventChannel.emit('currentSettings', {
						taskType: taskTypeFilter.value,
						photoStatus: photoStatusFilter.value,
						completionStatus: completionStatusFilter.value,
						mergeZoneTasks: mergeZoneTasks.value,
						taskSortMode: taskSortMode.value,
					});
				}
			} catch (error) {
				console.error('发送设置失败:', error);
			}
		},
	});
};

// 清空所有筛选条件
const clearAllFilters = () => {
	// 清空区域筛选
	clearDistrictFilter();
	// 清空小区筛选
	clearZoneFilter();
	// 清空任务类型筛选
	taskTypeFilter.value = 'all';
	// 清空拍照状态筛选
	photoStatusFilter.value = '';
	// 清空完成状态筛选
	completionStatusFilter.value = '';

	// 更新筛选条件
	setFilters({
		district: '',
		zone: 0,
		taskType: 'all',
		photoStatus: '',
		completionStatus: '',
		searchText: '',
	});

	// 清空搜索文本
	searchText.value = '';

	// 保存筛选状态
	saveFilterState('', '');
};

// 设置拍照状态筛选
const setPhotoStatusFilter = (status: string) => {
	photoStatusFilter.value = status;
	// 保存筛选状态
	saveFilterState(photoStatusFilter.value, completionStatusFilter.value);
};

// 设置任务类型筛选
const setTaskTypeFilter = (type: string) => {
	taskTypeFilter.value = type;
};

// 设置完成状态筛选
const setCompletionStatusFilter = (status: string) => {
	completionStatusFilter.value = status;
	// 保存筛选状态
	saveFilterState(photoStatusFilter.value, completionStatusFilter.value);
};

const pageLoad = async () => {
	// 先加载派单数据
	await loadDeliveries();

	// 如果有选中的派单ID，设置派单名称
	if (selectedDeliveryIds.value.length > 0) {
		// 总是显示第一个派单的名称（单派单或多派单的第一个）
		const deliveryId = selectedDeliveryIds.value[0];
		const delivery = deliveries.value.find((d: any) => d.deliveryId === deliveryId);
		if (delivery) {
			selectedDeliveryName.value = getDeliveryName(delivery.deliveryId);
		}
	}

	// 加载任务数据
	await loadTasks(false);

	// 注意：图片缓存检查现在由 smart-image 组件自动处理
};

// 注意：图片缓存检查现在由 smart-image 组件自动处理

// 强制更新函数
const forceUpdateStats = () => {
	forceUpdateTrigger.value++;
	console.info('📊 [TASK] 强制更新任务统计');
};

onMounted(() => {
	// 初始化字体大小
	initFontSize();

	// 初始化图片显示工具
	ImageDisplayUtils.initialize();

	// 监听字体大小变化
	uni.$on('fontSizeChanged', handleFontSizeChange);

	// 监听图片相关事件，强制更新统计
	uni.$on('imageAdded', forceUpdateStats);
	uni.$on('imageDeleted', forceUpdateStats);
	uni.$on('imageStatusChanged', forceUpdateStats);
	uni.$on('taskCompleted', forceUpdateStats);
	uni.$on('taskImagesUpdated', forceUpdateStats);

	// 执行页面加载逻辑
	pageLoad();
});

onShow(pageLoad);

onUnmounted(() => {
	// 清理事件监听
	uni.$off('fontSizeChanged', handleFontSizeChange);
	uni.$off('imageAdded', forceUpdateStats);
	uni.$off('imageDeleted', forceUpdateStats);
	uni.$off('imageStatusChanged', forceUpdateStats);
	uni.$off('taskCompleted', forceUpdateStats);
	uni.$off('taskImagesUpdated', forceUpdateStats);
});

// 状态筛选通过 filter-tabs 的自定义选项实现

// 区域相关
const selectedDistrictIndex = ref(0);
const selectedDistrictName = ref('');

// 计算区域详细信息（包括任务统计）- 基于所有任务（tasks）
const districtDetails = computed(() => {
	// 创建一个Map来存储每个区域的任务统计
	const districtMap = new Map();

	// 遍历所有任务，按区域分组并统计（基于 tasks，显示真实的区域任务总数）
	tasks.value.forEach((task: any) => {
		if (task.belongDistrict) {
			if (!districtMap.has(task.belongDistrict)) {
				districtMap.set(task.belongDistrict, {
					name: task.belongDistrict,
					totalTasks: 0, // 总任务数
					completedTasks: 0, // 已完成任务数
					incompleteTasks: 0, // 未完成任务数
					pendingUploadTasks: 0, // 待上传任务数
					tasks: [],
				});
			}

			// 添加任务到对应区域
			const districtData = districtMap.get(task.belongDistrict);
			districtData.tasks.push(task);
			districtData.totalTasks++; // 增加总任务数

			// 更新统计数据（基于所有任务）
			if (task.taskStatus === 'COMPLETED') {
				districtData.completedTasks++;
			}
		}
	});

	// 计算每个区域的未完成任务数和待上传任务数
	districtMap.forEach((districtData) => {
		// 未完成任务数 = 总任务数 - 已完成任务数
		districtData.incompleteTasks = districtData.totalTasks - districtData.completedTasks;

		// 待上传任务数：有图片需要上传的任务汇总
		districtData.pendingUploadTasks = districtData.tasks.filter((task: any) => {
			const stats = getTaskStatsReactive(task);
			return stats.pendingUpload > 0; // 有待上传图片的任务
		}).length;
	});

	// 转换为数组
	return Array.from(districtMap.values());
});

// 计算区域选项列表（用于旧代码兼容）
const districtOptions = computed(() => {
	return districtDetails.value.map((district: any) => district.name);
});

// 计算区域名称列表（用于picker显示）
const districtNames = computed(() => {
	return districtOptions.value;
});
</script>

<style lang="scss" scoped>
@mixin flex-center {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

@mixin image-container {
	position: relative;
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.task-container {
	min-height: 100vh;
	background-color: $uni-bg-color-grey;
	width: 100%;
	box-sizing: border-box;
	overflow-x: hidden;
	position: relative; /* 为筛选栏提供定位上下文 */

	// 应用字体缩放到文本元素
	text,
	.task-id,
	.label,
	.value,
	.delivery-item,
	.count-item,
	.empty-title,
	.empty-desc,
	.zone-name,
	.zone-district,
	.zone-address,
	.task-type-badge,
	.task-type-icon,
	.task-type-text,
	.stats-label-highlight,
	.stats-value-highlight {
		font-size: calc(1em * var(--font-scale, 1)) !important;
		line-height: calc(1.4 * var(--font-scale, 1)) !important;
	}

	// 大字体下的布局优化
	&[style*='--font-scale: 1.25'],
	&[style*='--font-scale: 1.5'] {
		.task-item {
			padding: calc(1.5rem * var(--font-scale, 1)) 0.1rem; /* 增加内边距 */

			.task-header {
				margin-bottom: calc(16rpx * var(--font-scale, 1)); /* 增加间距 */
			}

			.info-row {
				margin-bottom: calc(12rpx * var(--font-scale, 1)); /* 增加行间距 */

				.label {
					width: calc(140rpx * var(--font-scale, 1)); /* 调整标签宽度 */
				}

				// 大字体下的导航图标优化
				&.zone-name-row {
					.icon-navigation {
						width: calc(40rpx * var(--font-scale, 1)); /* 图标大小随字体缩放 */
						height: calc(40rpx * var(--font-scale, 1));
						font-size: calc(32rpx * var(--font-scale, 1));
						margin-left: calc(12rpx * var(--font-scale, 1)); /* 左间距随字体缩放 */
						margin-right: calc(20rpx * var(--font-scale, 1)); /* 右间距随字体缩放 */
					}
				}
			}

			// 大字体下的任务类型徽章优化
			.task-id-wrapper {
				.task-id-row {
					gap: calc(12rpx * var(--font-scale, 1)); /* 间距随字体缩放 */

					.task-type-badge {
						padding: calc(6rpx * var(--font-scale, 1)) calc(12rpx * var(--font-scale, 1));
						border-radius: 0; /* 去掉圆角 */
						border: none; /* 去掉边框 */

						.task-type-icon {
							margin-right: calc(6rpx * var(--font-scale, 1));
							font-size: calc(24rpx * var(--font-scale, 1));
						}
					}
				}

				// 大字体下的照片统计优化
				.task-photo-stats {
					gap: calc(24rpx * var(--font-scale, 1)); /* 间距随字体缩放 */
					padding: calc(16rpx * var(--font-scale, 1)) calc(20rpx * var(--font-scale, 1));
					margin-top: calc(12rpx * var(--font-scale, 1));

					.count-item {
						&.highlight-stats {
							padding: calc(8rpx * var(--font-scale, 1)) calc(16rpx * var(--font-scale, 1));
							border-radius: 0; /* 去掉圆角 */
							border: none; /* 去掉边框 */

							.stats-label-highlight {
								font-size: calc(26rpx * var(--font-scale, 1));
								margin-right: calc(4rpx * var(--font-scale, 1));

								&.zero-pending-label {
									color: #999999 !important; /* 待传为0时，标签显示灰色 */
								}
							}

							.stats-value-highlight {
								font-size: calc(30rpx * var(--font-scale, 1));

								&.pending,
								&.uploaded,
								&.completed,
								&.zero-pending-value,
								&:not(.pending):not(.uploaded):not(.completed):not(.zero-pending-value) {
									padding: 0; /* 去掉内边距 */
									border-radius: 0; /* 去掉圆角 */
								}

								&.zero-pending-value {
									color: #999999 !important; /* 待传为0时，数字显示灰色 */
								}
							}
						}
					}
				}
			}
		}
	}

	// 筛选选项卡定位
	:deep(.filter-tabs) {
		position: fixed;
		top: calc(44px + var(--status-bar-height)); // 导航栏高度 + 状态栏高度
		left: 0;
		right: 0;
		z-index: 99;
		width: 100%;
	}

	.view-toggle {
		padding: 10rpx;
		display: flex;
		justify-content: flex-end;

		.view-toggle-icon {
			width: 42rpx;
			height: 42rpx;
			padding: 6rpx;
			border-radius: 6rpx;
			background-color: $uni-bg-color;
		}
	}

	.filter-section {
		padding: 1.25rem;
		background-color: #fff;
		border-bottom: 0.125rem solid $uni-border-color;
		box-shadow: 0 0.25rem 0.75rem rgba(74, 144, 226, 0.1);
		position: sticky;
		top: var(--window-top);
		z-index: 1000;

		.filter-row {
			@include flex-center;
			gap: 10rpx;
			margin-top: 20rpx;

			.delivery-picker {
				flex: 1;
				@include flex-center;
				padding: 0.75rem 1.5rem;
				background-color: $uni-bg-color-grey;
				border-radius: 0.5rem;
				font-size: 1rem;

				.clear-filter {
					color: $uni-color-primary;
					font-size: 20rpx;
					padding-left: 8rpx;
				}
			}
		}
	}

	.task-list {
		flex: 1;
		height: calc(
			100vh - 44px - var(--status-bar-height) - 50px
		); /* 导航栏高度(44px) + 状态栏高度 + 筛选栏高度(50px) */
		margin-top: calc(44px + var(--status-bar-height) + 50px); /* 导航栏高度 + 状态栏高度 + 筛选栏高度 */
		width: 100%;
		box-sizing: border-box;
		padding: 0;
	}

	.empty-state {
		padding: 40rpx;
		text-align: center;
		color: $uni-text-color-grey;
		font-size: 28rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 60vh;

		.empty-icon {
			font-size: 60px;
			margin-bottom: 20px;
			color: #cccccc;
		}

		.empty-title {
			font-size: 18px;
			color: #666666;
			margin-bottom: 10px;
			font-weight: bold;
		}

		.empty-desc {
			font-size: 14px;
			color: #999999;
		}
	}

	.task-items {
		padding: 0;
		margin: 0;
		width: 100%;
	}

	.task-item {
		background-color: $uni-bg-color;
		padding: 1.5rem 0.1rem;
		margin-top: 0.2rem;
		margin-bottom: 0.1rem;
		border-bottom: 1px solid rgba(0, 0, 0, 0.05);
		display: flex;
		flex-direction: column; /* 改为垂直布局 */
		width: 100%;
		min-height: calc(8rem * var(--font-scale, 1)); /* 根据字体缩放调整最小高度 */

		// 第一行：图片和任务信息的容器
		.task-content-row {
			display: flex;
			align-items: stretch;
			width: 100%;
			flex: 1; /* 占据剩余空间 */
		}

		// 所有任务都有图片，统一布局
		.task-content-row .task-info {
			flex: 1;
			padding-left: 0;
		}

		.task-images {
			display: flex;
			flex-direction: column;
			gap: 16rpx;
			flex-shrink: 0;

			.task-image {
				width: 130rpx;
				padding: 20rpx;

				image {
					width: 130rpx;
					height: 130rpx;
					border-radius: 8rpx;
					object-fit: cover;
				}

				.default-image-placeholder {
					width: 130rpx;
					height: 130rpx;
					border-radius: 8rpx;
					background-color: #ffffff;
					border: 1px solid #e0e0e0;
					display: flex;
					align-items: center;
					justify-content: center;

					.default-image-text {
						font-size: 40rpx;
						color: #e0e0e0;
					}
				}

				.image-placeholder {
					width: 130rpx;
					height: 130rpx;
					border-radius: 8rpx;
					background-color: #f5f5f5;
					border: 1px dashed #ccc;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					cursor: pointer;
					transition: all 0.2s ease;

					.placeholder-icon {
						font-size: 32rpx;
						color: #999;
						margin-bottom: 4rpx;
					}

					.placeholder-text {
						font-size: 20rpx;
						color: #666;
						text-align: center;
						line-height: 1.2;
					}

					&:active {
						background-color: #eeeeee;
						transform: scale(0.98);
					}
				}
			}
		}

		.task-info {
			flex: 1;

			.task-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 16rpx;

				.task-id-container {
					display: flex;
					align-items: flex-start; /* 改为顶部对齐，适应多行文本 */
					justify-content: space-between;
					width: 100%;
					flex-wrap: wrap; /* 允许换行，适应大字体 */
					position: relative; /* 为绝对定位的导航图标提供参考 */
					gap: 8rpx; /* 添加间距 */

					.task-id-wrapper {
						display: flex;
						align-items: flex-start; /* 顶部对齐 */
						flex-direction: column; /* 改为垂直布局，避免水平挤压 */
						gap: 8rpx;
						flex: 1; /* 占据剩余空间 */
						min-width: 0; /* 允许内容收缩 */
						padding-right: 60rpx; /* 为导航图标留出空间 */

						.task-id-row {
							display: flex;
							align-items: center; /* 垂直居中对齐 */
							gap: 12rpx; /* 徽章与任务ID之间的间距 */
							width: 100%;
							flex-wrap: wrap; /* 允许换行，适应大字体 */

							.task-type-badge {
								display: flex;
								align-items: center;
								padding: 6rpx 12rpx;
								border-radius: 0; /* 去掉圆角 */
								font-size: 20rpx;
								font-weight: 700;
								flex-shrink: 0; /* 防止徽章被压缩 */
								border: none; /* 去掉边框 */

								.task-type-icon {
									margin-right: 6rpx;
									font-size: 24rpx;
								}

								.task-type-text {
									font-weight: 800;
									letter-spacing: 1rpx;
								}

								// 实景任务样式
								&.zone-task {
									background: transparent; /* 去掉背景 */
									color: #2e7d32;
									box-shadow: none; /* 去掉阴影 */
								}

								// 点位任务样式
								&.spot-task {
									background: transparent; /* 去掉背景 */
									color: #1565c0;
									box-shadow: none; /* 去掉阴影 */
								}
							}

							.task-id {
								flex: 1; /* 占据剩余空间 */
								min-width: 0; /* 允许内容收缩 */
							}

							.merged-count {
								flex-shrink: 0; /* 防止合并计数被压缩 */
							}
						}
					}

					.task-id {
						font-size: 28rpx;
						color: $uni-text-color;
						font-weight: bold;
						white-space: normal; /* 允许换行 */
						word-wrap: break-word; /* 长单词换行 */
						word-break: break-all; /* 必要时断词 */
						line-height: 1.4; /* 设置合适的行高 */
						max-width: 100%; /* 确保不超出容器 */
					}

					.merged-count {
						font-size: 24rpx;
						color: #ff6b00;
						margin-left: 8rpx;
						font-weight: normal;
					}
				}

				.task-status {
					font-size: 24rpx;
					padding: 4rpx 12rpx;
					border-radius: 4rpx;
					background-color: $uni-bg-color-grey;
					color: $uni-text-color-grey;

					&.通过 {
						background-color: $uni-bg-color-hover;
						color: $uni-color-success;
					}

					&.待审 {
						background-color: $uni-bg-color-hover;
						color: $uni-color-warning;
					}

					&.未过 {
						background-color: $uni-bg-color-hover;
						color: $uni-color-error;
					}
				}
			}

			.info-row {
				margin-bottom: 12rpx;
				font-size: 26rpx;
				line-height: 1.5;
				display: flex;
				flex-wrap: wrap; /* 允许换行 */
				align-items: flex-start; /* 顶部对齐 */

				.label {
					color: $uni-text-color-grey;
					width: 140rpx;
					flex-shrink: 0;
					margin-bottom: 4rpx; /* 在换行时提供间距 */
				}

				.value {
					color: $uni-text-color;
					flex: 1;
					min-width: 0; /* 允许内容收缩 */
					word-wrap: break-word; /* 长文本换行 */
					word-break: break-all; /* 必要时断词 */
				}

				// 楼盘名称行特殊样式
				&.zone-name-row {
					align-items: center; /* 垂直居中对齐 */

					.icon-navigation {
						width: 40rpx;
						height: 40rpx;
						cursor: pointer;
						font-size: 32rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						color: #2196f3;
						flex-shrink: 0; /* 防止图标被压缩 */
						margin-left: 12rpx; /* 与楼盘名称保持距离 */
						margin-right: 20rpx; /* 与屏幕右边缘保持距离 */
						background-color: rgba(33, 150, 243, 0.1); /* 添加背景色提高可见性 */
						border-radius: 50%; /* 圆形背景 */
						transition: all 0.2s ease; /* 添加过渡效果 */

						&:hover,
						&:active {
							background-color: rgba(33, 150, 243, 0.2);
							transform: scale(1.1);
						}
					}
				}

				.value-container {
					flex: 1;
					display: flex;
					flex-direction: column;

					.delivery-item {
						color: $uni-color-primary;
						font-weight: 600;
						margin-bottom: 4rpx;
						font-size: 26rpx;
						line-height: 1.4;

						&:last-child {
							margin-bottom: 0;
						}
					}
				}
			}
		}

		// 照片统计信息 - 使用整个任务项宽度，居中对齐
		.task-photo-stats {
			width: 100%;
			display: flex;
			justify-content: center; /* 居中对齐 */
			align-items: center;
			gap: 24rpx;
			padding: 16rpx 20rpx;
			margin-top: 12rpx;
			background-color: rgba(0, 0, 0, 0.02); /* 轻微背景色区分 */
			border-top: 1px solid rgba(0, 0, 0, 0.05); /* 顶部分隔线 */

			.count-item {
				padding: 4rpx 12rpx;
				border-radius: 4rpx;
				font-size: 24rpx;

				&.required {
					background-color: $uni-bg-color-hover;
					color: $uni-color-primary;
				}

				&.taken {
					background-color: $uni-bg-color-hover;
					color: $uni-color-success;
				}

				&.uploaded {
					background-color: $uni-bg-color-hover;
					color: $uni-color-warning;
				}

				&.pending {
					background-color: $uni-bg-color-hover;
					color: $uni-color-error;
				}

				// 突出显示统计数据
				&.highlight-stats {
					padding: 8rpx 16rpx;
					border-radius: 0; /* 去掉圆角 */
					font-weight: 700;
					box-shadow: none; /* 去掉阴影 */
					border: none; /* 去掉边框 */
					background: transparent; /* 去掉背景 */

					.stats-label-highlight {
						font-weight: 800;
						font-size: 26rpx;
						margin-right: 4rpx;

						&.zero-pending-label {
							color: #999999 !important; /* 待传为0时，标签显示灰色 */
						}

						&.incomplete-red {
							color: #dc3545 !important; /* 已拍+待传 < 应拍时，标签显示红色 */
						}
					}

					.stats-value-highlight {
						font-weight: 900;
						font-size: 30rpx;
						text-shadow: none; /* 去掉文字阴影 */

						&.pending {
							color: #dc3545 !important;
							background-color: transparent; /* 去掉背景色 */
							padding: 0; /* 去掉内边距 */
							border-radius: 0; /* 去掉圆角 */
						}

						&.uploaded {
							color: #198754 !important;
							background-color: transparent; /* 去掉背景色 */
							padding: 0; /* 去掉内边距 */
							border-radius: 0; /* 去掉圆角 */
						}

						&.incomplete-red {
							color: #dc3545 !important; /* 已拍+待传 < 应拍时显示红色 */
							background-color: transparent; /* 去掉背景色 */
							padding: 0; /* 去掉内边距 */
							border-radius: 0; /* 去掉圆角 */
						}

						&.completed {
							color: #198754 !important; /* 完成状态显示绿色 */
							background-color: transparent; /* 去掉背景色 */
							padding: 0; /* 去掉内边距 */
							border-radius: 0; /* 去掉圆角 */
						}

						&.zero-pending-value {
							color: #999999 !important; /* 待传为0时，数字显示灰色 */
							background-color: transparent; /* 去掉背景色 */
							padding: 0; /* 去掉内边距 */
							border-radius: 0; /* 去掉圆角 */
						}

						&:not(.pending):not(.uploaded):not(.completed):not(.zero-pending-value):not(.incomplete-red) {
							color: #0d6efd !important;
							background-color: transparent; /* 去掉背景色 */
							padding: 0; /* 去掉内边距 */
							border-radius: 0; /* 去掉圆角 */
						}
					}

					&.required {
						background: transparent; /* 去掉背景 */
						border: none; /* 去掉边框 */
						color: #1565c0;
					}

					&.uploaded {
						background: transparent; /* 去掉背景 */
						border: none; /* 去掉边框 */
						color: #2e7d32;
					}

					&.pending {
						background: transparent; /* 去掉背景 */
						border: none; /* 去掉边框 */
						color: #c62828;

						&.zero-pending {
							color: #999999; /* 待传为0时，整个项目显示灰色 */
						}
					}
				}
			}
		}
	}

	.zone-grid {
		padding: 0; /* 移除内边距，让卡片显示到边缘 */
		display: flex;
		flex-direction: column;
		width: 100%; /* 确保宽度为100% */

		.zone-card {
			background-color: $uni-bg-color;
			border-radius: 0; /* 移除圆角 */
			padding: 1.25rem;
			box-shadow: none; /* 移除阴影 */
			border-bottom: 1px solid #eee; /* 添加底部边框作为分隔线 */
			margin-bottom: 1px; /* 添加一点底部外边距，使分隔线更明显 */
			width: 100%; /* 确保宽度为100% */

			.zone-header {
				display: flex;
				align-items: center;
				gap: 8rpx;
				margin-bottom: 12rpx;

				.zone-name {
					font-size: 28rpx;
					font-weight: 700;
					color: $uni-text-color;
				}

				.zone-district {
					font-size: 24rpx;
					color: $uni-text-color-grey;
				}
			}

			.zone-address {
				font-size: 24rpx;
				color: $uni-text-color-grey;
				margin-bottom: 12rpx;
				word-wrap: break-word;
				white-space: pre-wrap;
			}

			.zone-stats {
				display: flex;
				justify-content: space-between;
				font-size: 24rpx;
				color: $uni-text-color;
			}
		}
	}
}

.zone-stats {
	.completed {
		color: #67c23a;
	}

	.pending {
		color: #e6a23c;
	}
}
</style>
