// 声明uni全局对象，解决TypeScript错误
declare const uni: any;

/**
 * Toast 图标类型
 */
export type ToastIcon = 'success' | 'none' | 'error' | 'loading';

/**
 * Toast 位置
 */
export type ToastPosition = 'top' | 'center' | 'bottom';

/**
 * Toast 优先级
 */
export enum ToastPriority {
  LOW = 1,      // 普通信息
  NORMAL = 2,   // 一般提示
  HIGH = 3,     // 重要提示
  URGENT = 4,   // 紧急提示
  ERROR = 5     // 错误信息（最高优先级）
}

/**
 * Toast 配置选项
 */
export interface ToastOptions {
  title: string;
  icon?: ToastIcon;
  image?: string;
  duration?: number;
  mask?: boolean;
  position?: ToastPosition;
  priority?: ToastPriority;
}

/**
 * 队列中的 Toast 项
 */
interface ToastQueueItem {
  options: ToastOptions;
  resolve: () => void;
  reject: (error: any) => void;
  timestamp: number;
}

/**
 * Toast 队列管理器
 */
class ToastManager {
  private queue: ToastQueueItem[] = [];
  private isShowing: boolean = false;
  private currentToast: ToastQueueItem | null = null;
  private currentTimer: number | null = null;

  /**
   * 添加 Toast 到队列
   */
  public enqueue(options: ToastOptions): Promise<void> {
    return new Promise((resolve, reject) => {
      const item: ToastQueueItem = {
        options: { ...options },
        resolve,
        reject,
        timestamp: Date.now()
      };

      // 设置默认优先级
      if (!item.options.priority) {
        item.options.priority = this.getDefaultPriority(item.options.icon);
      }

      // 如果是高优先级（ERROR），立即中断当前显示
      if (item.options.priority === ToastPriority.ERROR) {
        this.interruptCurrent();
        this.queue.unshift(item); // 插入到队列最前面
      } else {
        // 普通优先级，添加到队列末尾
        this.queue.push(item);
      }

      // 尝试处理队列
      this.processQueue();
    });
  }

  /**
   * 根据图标类型获取默认优先级
   */
  private getDefaultPriority(icon?: ToastIcon): ToastPriority {
    switch (icon) {
      case 'error':
        return ToastPriority.ERROR;
      case 'loading':
        return ToastPriority.HIGH;
      case 'success':
        return ToastPriority.NORMAL;
      default:
        return ToastPriority.LOW;
    }
  }

  /**
   * 中断当前显示的 Toast
   */
  private interruptCurrent(): void {
    if (this.isShowing && this.currentTimer) {
      // 清除当前计时器
      clearTimeout(this.currentTimer);
      this.currentTimer = null;

      // 隐藏当前 Toast
      uni.hideToast();

      // 完成当前 Toast 的 Promise
      if (this.currentToast) {
        this.currentToast.resolve();
        this.currentToast = null;
      }

      this.isShowing = false;
    }
  }

  /**
   * 处理队列
   */
  private processQueue(): void {
    // 如果正在显示或队列为空，不处理
    if (this.isShowing || this.queue.length === 0) {
      return;
    }

    // 按优先级排序队列（优先级高的在前面）
    this.queue.sort((a, b) => (b.options.priority || 0) - (a.options.priority || 0));

    // 取出优先级最高的 Toast
    const item = this.queue.shift();
    if (!item) return;

    this.showToastItem(item);
  }

  /**
   * 显示单个 Toast
   */
  private showToastItem(item: ToastQueueItem): void {
    this.isShowing = true;
    this.currentToast = item;

    const options = item.options;

    // 设置默认值
    options.icon = options.icon || 'none';
    options.duration = options.duration || 2000;

    // 调用原生 toast
    uni.showToast({
      title: options.title,
      icon: options.icon,
      image: options.image,
      duration: options.duration,
      mask: options.mask,
      position: options.position,
      success: () => {
        // 设置计时器，在指定时间后完成
        this.currentTimer = setTimeout(() => {
          this.completeCurrentToast();
        }, options.duration || 2000) as any;
      },
      fail: (error: any) => {
        this.completeCurrentToast(error);
      }
    });
  }

  /**
   * 完成当前 Toast 显示
   */
  private completeCurrentToast(error?: any): void {
    if (this.currentTimer) {
      clearTimeout(this.currentTimer);
      this.currentTimer = null;
    }

    if (this.currentToast) {
      if (error) {
        this.currentToast.reject(error);
      } else {
        this.currentToast.resolve();
      }
      this.currentToast = null;
    }

    this.isShowing = false;

    // 继续处理队列中的下一个 Toast
    setTimeout(() => {
      this.processQueue();
    }, 100); // 短暂延迟，避免 Toast 切换过快
  }

  /**
   * 清空队列
   */
  public clearQueue(): void {
    // 完成所有等待中的 Promise
    this.queue.forEach(item => {
      item.reject(new Error('Toast queue cleared'));
    });
    this.queue = [];
  }

  /**
   * 获取队列状态
   */
  public getQueueStatus() {
    return {
      queueLength: this.queue.length,
      isShowing: this.isShowing,
      currentPriority: this.currentToast?.options.priority || null
    };
  }
}

// 创建全局 Toast 管理器实例
const toastManager = new ToastManager();

/**
 * 显示提示信息
 * @param titleOrOptions - 提示文本或完整的配置选项
 * @param icon - 图标类型
 * @param duration - 显示时长（毫秒）
 * @returns Promise 在 toast 关闭后 resolve
 */
export const showToast = (
  titleOrOptions: string | ToastOptions,
  icon: ToastIcon = 'none',
  duration: number = 2000
): Promise<void> => {
  let options: ToastOptions;

  // 处理参数
  if (typeof titleOrOptions === 'string') {
    options = {
      title: titleOrOptions,
      icon,
      duration
    };
  } else {
    options = { ...titleOrOptions };
  }

  // 设置默认值
  options.icon = options.icon || 'none';
  options.duration = options.duration || 2000;

  // 添加到队列管理器
  return toastManager.enqueue(options);
};

/**
 * 显示成功提示
 * @param title - 提示文本
 * @param duration - 显示时长（毫秒）
 * @returns Promise 在 toast 关闭后 resolve
 */
export const showSuccessToast = (title: string, duration: number = 2000): Promise<void> => {
  return showToast({
    title,
    icon: 'success',
    duration,
    priority: ToastPriority.NORMAL
  });
};

/**
 * 显示错误提示（最高优先级，会立即中断当前显示）
 * @param title - 提示文本
 * @param duration - 显示时长（毫秒）
 * @returns Promise 在 toast 关闭后 resolve
 */
export const showErrorToast = (title: string, duration: number = 3000): Promise<void> => {
  return showToast({
    title,
    icon: 'error',
    duration,
    priority: ToastPriority.ERROR
  });
};

/**
 * 显示加载提示
 * @param title - 提示文本
 * @param duration - 显示时长（毫秒）
 * @returns Promise 在 toast 关闭后 resolve
 */
export const showLoadingToast = (title: string = '加载中...', duration: number = 2000): Promise<void> => {
  return showToast({
    title,
    icon: 'loading',
    duration,
    priority: ToastPriority.HIGH
  });
};

/**
 * 显示紧急提示（高优先级）
 * @param title - 提示文本
 * @param duration - 显示时长（毫秒）
 * @returns Promise 在 toast 关闭后 resolve
 */
export const showUrgentToast = (title: string, duration: number = 3000): Promise<void> => {
  return showToast({
    title,
    icon: 'none',
    duration,
    priority: ToastPriority.URGENT
  });
};

/**
 * 显示普通信息提示（低优先级）
 * @param title - 提示文本
 * @param duration - 显示时长（毫秒）
 * @returns Promise 在 toast 关闭后 resolve
 */
export const showInfoToast = (title: string, duration: number = 1500): Promise<void> => {
  return showToast({
    title,
    icon: 'none',
    duration,
    priority: ToastPriority.LOW
  });
};

/**
 * 隐藏提示
 */
export const hideToast = (): void => {
  uni.hideToast();
};

/**
 * 清空 Toast 队列
 */
export const clearToastQueue = (): void => {
  toastManager.clearQueue();
};

/**
 * 获取 Toast 队列状态
 */
export const getToastQueueStatus = () => {
  return toastManager.getQueueStatus();
};

// ToastPriority 已在上面导出，无需重复导出