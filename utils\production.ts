import { ENVIRONMENT } from './env';

// 声明全局uni对象
declare const uni: any;

/**
 * 生产环境配置和优化
 */
export class ProductionConfig {
  private static initialized = false;

  /**
   * 初始化生产环境配置
   */
  static initialize(): void {
    if (this.initialized || !ENVIRONMENT.isProduction) {
      return;
    }

    try {
      // 1. 禁用调试功能
      this.disableDebugFeatures();

      // 2. 设置错误处理
      this.setupErrorHandling();

      // 3. 优化性能
      this.optimizePerformance();

      // 4. 清理开发数据
      this.cleanupDevelopmentData();

      console.error('🚀 [PRODUCTION] 生产环境初始化完成');
      this.initialized = true;

    } catch (error) {
      console.error('❌ [PRODUCTION] 生产环境初始化失败:', error);
    }
  }

  /**
   * 禁用调试功能
   */
  private static disableDebugFeatures(): void {
    // 保存原始方法的引用
    const originalMethods = {
      log: console.info,
      info: console.info,
      debug: console.debug,
      trace: console.trace
    };

    // 禁用调试输出（保留error和warn）
    console.info = () => {};
    console.info = () => {};
    console.debug = () => {};
    console.trace = () => {};

    // 在需要时可以恢复（用于关键信息输出）
    (console as any)._restore = () => {
      Object.assign(console, originalMethods);
    };
  }

  /**
   * 设置全局错误处理
   */
  private static setupErrorHandling(): void {
    // 处理未捕获的Promise错误
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', (event) => {
        this.handleError(event.reason, 'Unhandled Promise Rejection');
        event.preventDefault();
      });

      // 处理全局JavaScript错误
      window.addEventListener('error', (event) => {
        this.handleError(event.error || event.message, 'Global JavaScript Error');
      });
    }

    // 处理uni-app特有的错误
    if (typeof uni !== 'undefined') {
      // 监听网络错误
      uni.onNetworkStatusChange?.((res: any) => {
        if (!res.isConnected) {
          this.handleError(new Error('网络连接断开'), 'Network Error');
        }
      });
    }
  }

  /**
   * 性能优化
   */
  private static optimizePerformance(): void {
    // 禁用开发者工具
    if (typeof window !== 'undefined') {
      // 检测开发者工具
      let devtools = false;
      setInterval(() => {
        if (window.outerHeight - window.innerHeight > 200 ||
            window.outerWidth - window.innerWidth > 200) {
          if (!devtools) {
            devtools = true;
            this.handleError(new Error('检测到开发者工具'), 'DevTools Detection');
          }
        } else {
          devtools = false;
        }
      }, 1000);
    }

    // 清理定时器和事件监听器
    this.setupCleanupHandlers();
  }

  /**
   * 清理开发环境数据
   */
  private static cleanupDevelopmentData(): void {
    try {
      // 清理开发环境的存储数据
      const devKeys = [
        'dev_test_data',
        'debug_info',
        'development_logs',
        'test_credentials',
        'mock_data'
      ];

      devKeys.forEach(key => {
        try {
          uni.removeStorageSync(key);
        } catch (error) {
          // 静默处理
        }
      });

    } catch (error) {
      console.error('清理开发数据失败:', error);
    }
  }

  /**
   * 设置清理处理器
   */
  private static setupCleanupHandlers(): void {
    // 页面卸载时清理
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.cleanup();
      });

      // 应用进入后台时清理
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          this.cleanup();
        }
      });
    }
  }

  /**
   * 错误处理
   */
  private static handleError(error: any, context: string = 'Unknown'): void {
    const errorInfo = {
      message: error?.message || String(error),
      stack: error?.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown',
      url: typeof window !== 'undefined' ? window.location?.href : 'Unknown'
    };

    // 输出到控制台（生产环境仍需要错误日志）
    console.error(`[Production Error - ${context}]:`, errorInfo);

    // 这里可以添加错误上报逻辑
    // 例如：发送到错误监控服务
    this.reportError(errorInfo);
  }

  /**
   * 错误上报（可扩展）
   */
  private static reportError(errorInfo: any): void {
    // 这里可以实现错误上报逻辑
    // 例如：发送到Sentry、Bugsnag等错误监控服务

    try {
      // 示例：发送到自定义错误收集API
      // fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorInfo)
      // }).catch(() => {
      //   // 静默处理上报失败
      // });

    } catch (error) {
      // 静默处理上报错误
    }
  }

  /**
   * 清理资源
   */
  private static cleanup(): void {
    try {
      // 清理可能的内存泄漏
      // 例如：清理定时器、事件监听器等

      // 清理临时数据
      if (typeof uni !== 'undefined') {
        try {
          uni.removeStorageSync('temp_data');
          uni.removeStorageSync('cache_data');
        } catch (error) {
          // 静默处理
        }
      }

    } catch (error) {
      console.error('资源清理失败:', error);
    }
  }

  /**
   * 获取应用信息
   */
  static getAppInfo(): any {
    return {
      environment: ENVIRONMENT.current,
      timestamp: new Date().toISOString(),
      platform: typeof uni !== 'undefined' ? 'uni-app' : 'web',
      version: '2.0.0'
    };
  }
}

// 自动初始化
ProductionConfig.initialize();
