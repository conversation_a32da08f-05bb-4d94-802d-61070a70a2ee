import { ENVIRONMENT } from './env';

/**
 * 日志级别枚举
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

/**
 * 生产环境日志管理器
 */
export class Logger {
  private static currentLevel: LogLevel = ENVIRONMENT.isProduction ? LogLevel.ERROR : LogLevel.DEBUG;

  /**
   * 设置日志级别
   */
  static setLevel(level: LogLevel) {
    this.currentLevel = level;
  }

  /**
   * 调试日志
   */
  static debug(message: string, ...args: any[]) {
    if (this.currentLevel <= LogLevel.DEBUG) {
      console.info(`🐛 [DEBUG] ${message}`, ...args);
    }
  }

  /**
   * 信息日志
   */
  static info(message: string, ...args: any[]) {
    if (this.currentLevel <= LogLevel.INFO) {
      console.info(`ℹ️ [INFO] ${message}`, ...args);
    }
  }

  /**
   * 警告日志
   */
  static warn(message: string, ...args: any[]) {
    if (this.currentLevel <= LogLevel.WARN) {
      console.warn(`⚠️ [WARN] ${message}`, ...args);
    }
  }

  /**
   * 错误日志
   */
  static error(message: string, ...args: any[]) {
    if (this.currentLevel <= LogLevel.ERROR) {
      console.error(`❌ [ERROR] ${message}`, ...args);
    }
  }

  /**
   * 网络请求日志
   */
  static request(message: string, ...args: any[]) {
    if (this.currentLevel <= LogLevel.DEBUG) {
      console.info(`🌐 [REQUEST] ${message}`, ...args);
    }
  }

  /**
   * API调用日志
   */
  static api(message: string, ...args: any[]) {
    if (this.currentLevel <= LogLevel.DEBUG) {
      console.info(`🔐 [API] ${message}`, ...args);
    }
  }

  /**
   * 任务相关日志
   */
  static task(message: string, ...args: any[]) {
    if (this.currentLevel <= LogLevel.DEBUG) {
      console.info(`📋 [TASK] ${message}`, ...args);
    }
  }

  /**
   * 字体相关日志
   */
  static font(message: string, ...args: any[]) {
    if (this.currentLevel <= LogLevel.DEBUG) {
      console.info(`🎨 [FONT] ${message}`, ...args);
    }
  }

  /**
   * 环境相关日志
   */
  static env(message: string, ...args: any[]) {
    if (this.currentLevel <= LogLevel.INFO) {
      console.info(`🌍 [ENV] ${message}`, ...args);
    }
  }
}

// 根据环境设置默认日志级别
if (ENVIRONMENT.isProduction) {
  Logger.setLevel(LogLevel.ERROR); // 生产环境只显示错误
} else if (ENVIRONMENT.isTest) {
  Logger.setLevel(LogLevel.WARN);  // 测试环境显示警告及以上
} else {
  Logger.setLevel(LogLevel.DEBUG); // 开发环境显示所有日志
}

// 导出便捷方法
export const log = Logger;
