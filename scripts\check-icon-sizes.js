#!/usr/bin/env node

/**
 * 检查图标大小设置脚本
 * 
 * 功能：
 * 1. 扫描Vue文件中图标的CSS样式设置
 * 2. 检查font-size、transform scale、width、height等
 * 3. 按区域分组显示不一致的设置
 */

const fs = require('fs');
const path = require('path');

// 项目根目录
const ROOT_DIR = path.resolve(__dirname, '..');

// Vue文件列表
const VUE_FILES = [
  'pages/delivery/index.vue',
  'pages/delivery/components/delivery-navbar.vue',
  'pages/images/components/images-navbar.vue',
  'pages/my/index.vue',
  'components/custom-tabbar.vue',
  'pages/task/index.vue',
  'pages/task/task-detail.vue',
  'pages/task/components/task-navbar.vue'
];

console.log('📏 检查图标大小设置');
console.log('================================');

// 提取CSS中的图标样式
function extractIconStyles(content, filePath) {
  const iconStyles = [];
  
  // 匹配CSS规则中的图标样式
  const cssRuleRegex = /\.([^{]+)\s*\{([^}]+)\}/g;
  let match;
  
  while ((match = cssRuleRegex.exec(content)) !== null) {
    const selector = match[1].trim();
    const rules = match[2];
    
    // 检查是否是图标相关的选择器
    if (selector.includes('icon') || selector.includes('iconfont')) {
      const styles = {};
      
      // 提取font-size
      const fontSizeMatch = rules.match(/font-size:\s*([^;]+)/);
      if (fontSizeMatch) {
        styles.fontSize = fontSizeMatch[1].trim();
      }
      
      // 提取transform
      const transformMatch = rules.match(/transform:\s*([^;]+)/);
      if (transformMatch) {
        styles.transform = transformMatch[1].trim();
      }
      
      // 提取width
      const widthMatch = rules.match(/width:\s*([^;]+)/);
      if (widthMatch) {
        styles.width = widthMatch[1].trim();
      }
      
      // 提取height
      const heightMatch = rules.match(/height:\s*([^;]+)/);
      if (heightMatch) {
        styles.height = heightMatch[1].trim();
      }
      
      if (Object.keys(styles).length > 0) {
        iconStyles.push({
          selector,
          styles,
          filePath
        });
      }
    }
  }
  
  return iconStyles;
}

// 分析Vue文件中的图标样式
function analyzeIconStyles() {
  const allStyles = [];
  
  VUE_FILES.forEach(filePath => {
    const fullPath = path.join(ROOT_DIR, filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // 提取<style>标签中的内容
      const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/g;
      let styleMatch;
      
      while ((styleMatch = styleRegex.exec(content)) !== null) {
        const styleContent = styleMatch[1];
        const iconStyles = extractIconStyles(styleContent, filePath);
        allStyles.push(...iconStyles);
      }
    }
  });
  
  return allStyles;
}

// 按文件分组显示样式
function displayStylesByFile(styles) {
  const stylesByFile = {};
  
  styles.forEach(style => {
    if (!stylesByFile[style.filePath]) {
      stylesByFile[style.filePath] = [];
    }
    stylesByFile[style.filePath].push(style);
  });
  
  Object.entries(stylesByFile).forEach(([filePath, fileStyles]) => {
    console.log(`\n📄 ${filePath}:`);
    console.log('-'.repeat(50));
    
    fileStyles.forEach(style => {
      console.log(`🎯 ${style.selector}:`);
      Object.entries(style.styles).forEach(([prop, value]) => {
        console.log(`   ${prop}: ${value}`);
      });
      console.log('');
    });
  });
}

// 检查大小不一致的问题
function checkInconsistencies(styles) {
  console.log('\n⚠️  大小不一致检查:');
  console.log('================================');
  
  // 按区域分组
  const regions = {
    'navbar': styles.filter(s => s.selector.includes('navbar') || s.selector.includes('icon')),
    'tabbar': styles.filter(s => s.filePath.includes('tabbar')),
    'task': styles.filter(s => s.filePath.includes('task')),
    'delivery': styles.filter(s => s.filePath.includes('delivery')),
    'images': styles.filter(s => s.filePath.includes('images')),
    'my': styles.filter(s => s.filePath.includes('my'))
  };
  
  Object.entries(regions).forEach(([regionName, regionStyles]) => {
    if (regionStyles.length === 0) return;
    
    console.log(`\n🏷️  ${regionName.toUpperCase()} 区域:`);
    
    // 收集所有font-size值
    const fontSizes = new Set();
    const transforms = new Set();
    const widths = new Set();
    const heights = new Set();
    
    regionStyles.forEach(style => {
      if (style.styles.fontSize) fontSizes.add(style.styles.fontSize);
      if (style.styles.transform) transforms.add(style.styles.transform);
      if (style.styles.width) widths.add(style.styles.width);
      if (style.styles.height) heights.add(style.styles.height);
    });
    
    if (fontSizes.size > 1) {
      console.log(`   ⚠️  font-size 不一致: ${[...fontSizes].join(', ')}`);
    } else if (fontSizes.size === 1) {
      console.log(`   ✅ font-size 一致: ${[...fontSizes][0]}`);
    }
    
    if (transforms.size > 1) {
      console.log(`   ⚠️  transform 不一致: ${[...transforms].join(', ')}`);
    } else if (transforms.size === 1) {
      console.log(`   ✅ transform 一致: ${[...transforms][0]}`);
    }
    
    if (widths.size > 1) {
      console.log(`   ⚠️  width 不一致: ${[...widths].join(', ')}`);
    } else if (widths.size === 1) {
      console.log(`   ✅ width 一致: ${[...widths][0]}`);
    }
    
    if (heights.size > 1) {
      console.log(`   ⚠️  height 不一致: ${[...heights].join(', ')}`);
    } else if (heights.size === 1) {
      console.log(`   ✅ height 一致: ${[...heights][0]}`);
    }
  });
}

// 生成统一建议
function generateUnificationSuggestions(styles) {
  console.log('\n💡 统一建议:');
  console.log('================================');
  
  // 统计最常用的值
  const fontSizeCount = {};
  const transformCount = {};
  
  styles.forEach(style => {
    if (style.styles.fontSize) {
      fontSizeCount[style.styles.fontSize] = (fontSizeCount[style.styles.fontSize] || 0) + 1;
    }
    if (style.styles.transform) {
      transformCount[style.styles.transform] = (transformCount[style.styles.transform] || 0) + 1;
    }
  });
  
  // 找出最常用的值
  const mostCommonFontSize = Object.entries(fontSizeCount)
    .sort(([,a], [,b]) => b - a)[0]?.[0];
  const mostCommonTransform = Object.entries(transformCount)
    .sort(([,a], [,b]) => b - a)[0]?.[0];
  
  console.log('建议统一标准:');
  if (mostCommonFontSize) {
    console.log(`📏 font-size: ${mostCommonFontSize} (使用频率最高)`);
  }
  if (mostCommonTransform) {
    console.log(`🔄 transform: ${mostCommonTransform} (使用频率最高)`);
  }
  
  console.log('\n推荐的图标大小规范:');
  console.log('- 导航栏图标: font-size: 16px, transform: scale(1.1)');
  console.log('- 标签栏图标: font-size: 16px, transform: scale(1.0)');
  console.log('- 内容区图标: font-size: 14px, transform: scale(1.0)');
  console.log('- 大图标: font-size: 20px, transform: scale(1.0)');
}

// 主函数
function main() {
  const styles = analyzeIconStyles();
  
  console.log(`找到 ${styles.length} 个图标样式定义\n`);
  
  // 按文件显示样式
  displayStylesByFile(styles);
  
  // 检查不一致问题
  checkInconsistencies(styles);
  
  // 生成统一建议
  generateUnificationSuggestions(styles);
}

// 运行脚本
main();
