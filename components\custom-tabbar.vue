<template>
  <view class="custom-tabbar">
    <view
      v-for="(item, index) in tabList"
      :key="index"
      class="tab-item"
      :class="{ active: currentPage === item.pagePath }"
      @click="switchTab(item.pagePath)"
    >
      <text class="tab-icon iconfont" :class="getTabIconClass(index)"></text>
      <text class="tab-text">{{ item.text }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';

// 声明全局 uni 对象，解决 TypeScript 错误
declare const uni: any;

// 当前页面路径
const currentPage = ref('');

// 标签栏配置
const tabList = [
  {
    pagePath: 'pages/delivery/index',
    text: '派单'
  },
  {
    pagePath: 'pages/task/index',
    text: '任务'
  },
  {
    pagePath: 'pages/images/index',
    text: '上传'
  },
  {
    pagePath: 'pages/my/index',
    text: '我的'
  }
];

// 获取当前页面路径
onMounted(() => {
  const pages = getCurrentPages();
  if (pages.length > 0) {
    const currentPageObj = pages[pages.length - 1];
    currentPage.value = currentPageObj.route;
  }

  // 监听页面切换
  uni.$on('tabChange', (pagePath: string) => {
    currentPage.value = pagePath;
  });
});

// 切换标签页
const switchTab = (pagePath: string) => {
  if (currentPage.value === pagePath) return;

  uni.switchTab({
    url: `/${pagePath}`
  });
};

// 获取标签图标类名
const getTabIconClass = (index: number): string => {
  const iconClasses = ['icon-delivery', 'icon-task', 'icon-queue', 'icon-my'];
  return iconClasses[index] || 'icon-clipboard';
};
</script>

<style lang="scss" scoped>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: #ffffff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);
  z-index: 999;

  .tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    flex: 1;

    .tab-icon {
      font-size: 24px;
      margin-bottom: 2px;
      color: #333333;
    }

    .tab-text {
      font-size: 12px;
      color: #333333;
    }

    &.active {
      .tab-icon, .tab-text {
        color: #2979FF;
      }
    }
  }
}
</style>
