import { Delivery } from './Delivery';
import { Task } from './Task';
import { TaskImages } from './TaskImages';

// 派单状态统计
export interface DeliveryState {
    totalCount: number;
    completeCount: number;
    uncompleteCount: number;

    spotTotalCount: number,
    spotCompletedCount: number,
    spotUncompleteCount: number,
    zoneTotalCount: number,
    zoneCompletedCount: number,
    zoneUncompleteCount: number
}
// 任务状态统计
export interface TaskState {
    requiredCount: number; // 要求完成数量
    hasTaken: number; // 已完成数量
    hasPending: number; // 待上传数量
}

export interface UploadState {
    totalCount: number;
    queueCount: number;
    completeCount: number;
    errorCount: number;
}

// 客户端静态数据存储
export interface ClientStore {
    deliveryMap: Map<string, Delivery>;
    taskMap: Map<string, Task>;
    imageMap: Map<string, TaskImages>;
    uploadMap: Map<string, TaskImages>;
}