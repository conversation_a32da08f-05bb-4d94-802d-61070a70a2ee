import { Delivery } from './Delivery';
import { Task } from './Task';
import { TaskImages } from './TaskImages';

// 派单状态统计
export interface DeliveryState {
	totalCount: number;
	completeCount: number;
	uncompleteCount: number;

	spotTotalCount: number;
	spotCompletedCount: number;
	spotUncompleteCount: number;
	zoneTotalCount: number;
	zoneCompletedCount: number;
	zoneUncompleteCount: number;
}
// 任务状态统计
export interface TaskState {
	shouldTake: number; // 应拍（服务端）
	hasTaken: number; // 已拍（服务端已确认）
	hasUploaded: number; // 已传（客户端临时，很快会被清空）
	pendingUpload: number; // 待传（客户端本地）
}

export interface UploadState {
	totalCount: number;
	queueCount: number;
	completeCount: number;
	errorCount: number;
}

// 客户端静态数据存储
export interface ClientStore {
	deliveryMap: Map<string, Delivery>;
	taskMap: Map<string, Task>;
	imageMap: Map<string, TaskImages>;
	uploadMap: Map<string, TaskImages>;
}
