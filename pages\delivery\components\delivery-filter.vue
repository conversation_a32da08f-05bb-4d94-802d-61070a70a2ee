<template>
  <view class="filter-tabs" @click.stop>
    <!-- 主筛选选项卡 -->
    <view class="main-tabs">
      <view
        class="tab-item"
        v-for="(tab, index) in tabs"
        :key="index"
        :class="{ active: activeTabIndex === index }"
        @click.stop="selectTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
        <text class="tab-icon">{{ activeTabIndex === index && showDropdown ? '▲' : '▼' }}</text>
      </view>
    </view>

    <!-- 下拉菜单 -->
    <view class="dropdown-menu" v-if="showDropdown">
      <view
        class="dropdown-item"
        v-for="(item, index) in dropdownItems"
        :key="index"
        :class="{ active: tabs[activeTabIndex].value === item.value }"
        @click.stop="selectDropdownItem(item)"
      >
        <text class="dropdown-text">{{ item.name }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

// 定义组件属性
const props = defineProps({
  initialMergeType: {
    type: String,
    default: 'no_merge'
  },
  initialSortOrder: {
    type: String,
    default: 'time_asc'
  }
});

// 定义筛选选项卡
const tabs = ref([
  { name: '不合并任务', value: 'no_merge' },
  { name: '时间正序', value: 'time_asc' }
]);

// 当前激活的选项卡索引
const activeTabIndex = ref(0);

// 是否显示下拉菜单
const showDropdown = ref(false);

// 初始化选项卡值
onMounted(() => {
  // 设置合并类型
  if (props.initialMergeType === 'merge_today') {
    tabs.value[0].name = '合并当天任务';
    tabs.value[0].value = 'merge_today';
  } else if (props.initialMergeType === 'manual_merge') {
    tabs.value[0].name = '手动选择合并';
    tabs.value[0].value = 'manual_merge';
  }

  // 设置排序方式
  if (props.initialSortOrder === 'time_desc') {
    tabs.value[1].name = '时间倒序';
    tabs.value[1].value = 'time_desc';
  }
});

// 下拉菜单选项
const dropdownItems = computed(() => {
  if (activeTabIndex.value === 0) {
    return [
      { name: '不合并任务', value: 'no_merge' },
      { name: '合并当天任务', value: 'merge_today' },
      { name: '手动选择合并', value: 'manual_merge' }
    ];
  } else {
    return [
      { name: '时间正序', value: 'time_asc' },
      { name: '时间倒序', value: 'time_desc' }
    ];
  }
});

// 定义事件
const emit = defineEmits(['filter-change']);

// 选择选项卡
const selectTab = (index: number) => {
  if (activeTabIndex.value === index) {
    // 如果点击的是当前激活的选项卡，则切换下拉菜单的显示状态
    showDropdown.value = !showDropdown.value;
  } else {
    // 如果点击的是其他选项卡，则切换到该选项卡并显示下拉菜单
    activeTabIndex.value = index;
    showDropdown.value = true;
  }
};

// 点击外部关闭下拉菜单
onMounted(() => {
  // 仅在H5环境中添加document事件监听器
  // #ifdef H5
  if (typeof document !== 'undefined') {
    document.addEventListener('click', (event) => {
      // 如果点击的不是筛选选项卡内的元素，则关闭下拉菜单
      const target = event.target as HTMLElement;
      if (!target.closest('.filter-tabs')) {
        showDropdown.value = false;
      }
    });
  }
  // #endif

  // 在非H5环境中，可以使用其他方式处理点击外部关闭下拉菜单
  // 例如，可以在父组件中处理页面点击事件
});

// 选择下拉菜单项
const selectDropdownItem = (item: any) => {
  // 更新当前选项卡的名称
  tabs.value[activeTabIndex.value].name = item.name;
  tabs.value[activeTabIndex.value].value = item.value;

  // 隐藏下拉菜单
  showDropdown.value = false;

  // 触发筛选变更事件
  emit('filter-change', {
    tabIndex: activeTabIndex.value,
    value: item.value
  });
};
</script>

<style lang="scss" scoped>
.filter-tabs {
  position: relative;
  width: 100%;

  .main-tabs {
    display: flex;
    width: 100%;
    height: 50px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #EEEEEE;

    .tab-item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;

      &.active {
        color: #0CBFB0;
      }

      .tab-text {
        font-size: 16px;
        margin-right: 5px;
      }

      .tab-icon {
        font-size: 12px;
      }
    }
  }

  .dropdown-menu {
    position: absolute;
    width: 100%;
    background-color: #FFFFFF;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 100;
    border-top: 1px solid #EEEEEE;

    .dropdown-item {
      height: 50px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      border-bottom: 1px solid #F5F5F5;

      &:active {
        background-color: #F5F5F5;
      }

      &.active {
        color: #0CBFB0;
        font-weight: bold;
        position: relative;

        &::after {
          content: "✓";
          position: absolute;
          right: 20px;
          color: #0CBFB0;
          font-size: 18px;
        }
      }

      .dropdown-text {
        font-size: 16px;
      }
    }
  }
}
</style>
