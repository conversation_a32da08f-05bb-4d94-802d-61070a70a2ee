<template>
  <view class="filter-dialog" :style="dynamicFontStyle">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <view class="dialog-header">
      <view class="back-button" @click="goBack">
        <text class="iconfont icon-back"></text>
      </view>
      <view class="title">派单显示设置</view>
      <view class="reset-button" @click="resetSettings">
        <text>重置设置</text>
      </view>
    </view>

    <view class="filter-section">
      <view class="section-title">派单完成状态</view>
      <radio-group @change="onCompletionStatusChange">
        <view class="option-list">
          <view class="option-item" @click="selectCompletionStatus('all')">
            <radio value="all" :checked="completionStatus === 'all'" color="#6CA5F2" />
            <text class="option-text">全部</text>
          </view>
          <view class="option-item" @click="selectCompletionStatus('incomplete')">
            <radio value="incomplete" :checked="completionStatus === 'incomplete'" color="#6CA5F2" />
            <text class="option-text">仅显示未完成</text>
          </view>
          <view class="option-item" @click="selectCompletionStatus('complete')">
            <radio value="complete" :checked="completionStatus === 'complete'" color="#6CA5F2" />
            <text class="option-text">仅显示已完成</text>
          </view>
        </view>
      </radio-group>
    </view>

    <view class="filter-section">
      <view class="section-title">派单期间范围</view>
      <radio-group @change="onOrderPeriodChange">
        <view class="option-list">
          <view class="option-item" @click="selectOrderPeriod('today')">
            <radio value="today" :checked="orderPeriod === 'today'" color="#6CA5F2" />
            <text class="option-text">当天</text>
          </view>
          <view class="option-item" @click="selectOrderPeriod('week')">
            <radio value="week" :checked="orderPeriod === 'week'" color="#6CA5F2" />
            <text class="option-text">前后一周</text>
          </view>
          <view class="option-item" @click="selectOrderPeriod('halfMonth')">
            <radio value="halfMonth" :checked="orderPeriod === 'halfMonth'" color="#6CA5F2" />
            <text class="option-text">前后半月</text>
          </view>
          <view class="option-item" @click="selectOrderPeriod('month')">
            <radio value="month" :checked="orderPeriod === 'month'" color="#6CA5F2" />
            <text class="option-text">前后一月</text>
          </view>
        </view>
      </radio-group>
      <view class="description-section">
        <text class="description-item">派单期间范围：决定显示哪个时间范围内的派单</text>
        <text class="description-item">当天：只显示今天的派单</text>
        <text class="description-item">前后一周：显示前后一周内的派单</text>
        <text class="description-item">前后半月：显示前后半个月内的派单</text>
        <text class="description-item">前后一月：显示前后一个月内的派单</text>
      </view>
    </view>

  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, onBeforeUnmount, computed } from 'vue';
import { useSettingsStore } from '../../stores/settings';
import { showSuccessToast } from '../../utils/toast';

// 声明全局 uni 对象和函数，解决 TypeScript 错误
declare const uni: any;
declare const getCurrentPages: () => any[];

// 获取设置 store
const settingsStore = useSettingsStore();

// 状态栏高度
const statusBarHeight = ref(20);

// 字体大小管理
const currentFontScale = ref(1);

// 动态字体样式
const dynamicFontStyle = computed(() => ({
  '--font-scale': currentFontScale.value
}));

// 初始化字体大小
const initFontSize = () => {
  try {
    const savedFontScale = uni.getStorageSync('fontScale') || 1;
    currentFontScale.value = savedFontScale;
    console.info(`📄 [DELIVERY-SETTINGS] 页面字体大小初始化: 缩放 ${savedFontScale}`);
  } catch (error) {
    console.error('页面字体大小初始化失败:', error);
  }
};

// 处理字体大小变化
const handleFontSizeChange = (data: any) => {
  const { size, scale } = data;
  currentFontScale.value = scale;
  console.info(`📄 [DELIVERY-SETTINGS] 页面字体大小已更新: ${size} (缩放: ${scale})`);
};

// 完成状态 - 默认为仅显示未完成
const completionStatus = ref(settingsStore.settings.deliveryDisplaySettings?.completionStatus || 'incomplete');
// 派单期间范围 - 从用户设置中获取
const orderPeriod = ref(settingsStore.settings.orderPeriod || 'week');

// 选择完成状态
const selectCompletionStatus = (status: string) => {
  completionStatus.value = status;
};

// radio-group change事件处理
const onCompletionStatusChange = (e: any) => {
  completionStatus.value = e.detail.value;
};

// 选择派单期间范围
const selectOrderPeriod = (period: string) => {
  orderPeriod.value = period;
};

// radio-group change事件处理
const onOrderPeriodChange = (e: any) => {
  orderPeriod.value = e.detail.value;
};

// 获取事件通道
const getEventChannel = () => {
  try {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    return currentPage.getOpenerEventChannel();
  } catch (error) {
    console.error('获取事件通道失败:', error);
    return null;
  }
};

// 保存设置到 store
const saveSettingsToStore = () => {
  try {
    // 确保 deliveryDisplaySettings 存在
    if (!settingsStore.settings.deliveryDisplaySettings) {
      settingsStore.settings.deliveryDisplaySettings = {};
    }

    // 更新 store 中的派单显示设置
    settingsStore.settings.deliveryDisplaySettings.completionStatus = completionStatus.value;
    settingsStore.settings.orderPeriod = orderPeriod.value;

    // 保存设置到本地存储
    settingsStore.saveSettings();
    console.info('派单设置已保存到 store');
  } catch (error) {
    console.error('保存派单设置到 store 失败:', error);
  }
};

// 关闭时应用设置
const applySettingsOnClose = () => {
  try {
    // 创建设置对象
    const settings = {
      completionStatus: completionStatus.value,
      orderPeriod: orderPeriod.value
    };

    // 保存设置到 store
    saveSettingsToStore();

    // 通过事件通道发送设置
    const eventChannel = getEventChannel();
    if (eventChannel) {
      eventChannel.emit('settingsResult', settings);
    }

    console.info('派单设置已应用:', settings);
  } catch (error) {
    console.error('应用派单设置失败:', error);
  }
};

// 重置设置
const resetSettings = () => {
  // 重置为默认值
  completionStatus.value = 'incomplete'; // 默认仅显示未完成
  orderPeriod.value = 'week'; // 默认前后一周

  // 显示提示
  showSuccessToast('已重置为默认设置');
};

// 返回上一页
const goBack = () => {
  // 在返回前应用设置
  applySettingsOnClose();
  uni.navigateBack();
};

// 页面加载时，获取当前设置
onMounted(() => {
  // 获取状态栏高度
  try {
    const sysInfo = uni.getSystemInfoSync();
    statusBarHeight.value = sysInfo.statusBarHeight || 20;
  } catch (error) {
    console.warn('获取状态栏高度失败:', error);
    statusBarHeight.value = 20; // 使用默认值
  }

  // 初始化字体大小
  initFontSize();

  // 监听字体大小变化
  uni.$on('fontSizeChanged', handleFontSizeChange);

  // 延迟一下，确保页面已经准备好
  setTimeout(() => {
    try {
      // 尝试从事件通道获取当前会话的设置
      const eventChannel = getEventChannel();
      if (eventChannel) {
        eventChannel.on('currentSettings', (data: any) => {
          if (data) {
            // 应用从事件通道获取的设置
            if (data.completionStatus) {
              completionStatus.value = data.completionStatus;
            }
            if (data.orderPeriod) {
              orderPeriod.value = data.orderPeriod;
            }

            // 保存设置到 store
            saveSettingsToStore();
          }
        });
      }
    } catch (error) {
      console.error('监听设置事件失败:', error);
    }
  }, 100);
});

// 页面卸载前应用设置（处理手势关闭等情况）
onBeforeUnmount(() => {
  applySettingsOnClose();
});

// 页面卸载时的清理
onUnmounted(() => {
  // 清理字体大小监听
  uni.$off('fontSizeChanged', handleFontSizeChange);
  console.info('派单设置页面已卸载');
});
</script>

<style lang="scss" scoped>
.filter-dialog {
  padding: 0 0 30px 0;
  background-color: #f5f5f5;
  min-height: 100vh;

  // 应用字体缩放到所有文本元素
  .title, .reset-button, .section-title, .option-text, .description-item {
    font-size: calc(1em * var(--font-scale, 1)) !important;
  }

  .status-bar {
    background-color: #6CA5F2; // 派单页面使用蓝色
    width: 100%;
  }

  .dialog-header {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background-color: #6CA5F2; // 派单页面使用蓝色
    color: #FFFFFF;
    height: 50px;
    position: sticky;
    top: 0;
    z-index: 100;

    .back-button {
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;

      .iconfont {
        font-size: 16px;
        color: #FFFFFF;
        transform: scale(1.1);
      }
    }

    .title {
      flex: 1;
      text-align: center;
      font-size: calc(18px * var(--font-scale, 1));
      font-weight: bold;
    }

    .reset-button {
      font-size: calc(14px * var(--font-scale, 1));
      color: #FFFFFF;
      opacity: 0.9;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .filter-section {
    margin: 15px;
    background-color: #FFFFFF;
    border-radius: 8px;
    overflow: hidden;

    .section-title {
      padding: 15px;
      font-size: calc(16px * var(--font-scale, 1));
      font-weight: bold;
      border-bottom: 1px solid #f0f0f0;
    }

    .option-list {
      .option-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s ease;
        cursor: pointer;

        &:hover {
          background-color: #f8f8f8;
        }

        &:active {
          background-color: #f0f0f0;
        }

        .option-text {
          margin-left: 10px;
          font-size: calc(16px * var(--font-scale, 1));
          transition: color 0.2s ease;
        }
      }
    }

    .description-section {
      padding: 15px;
      background-color: #f9f9f9;

      .description-item {
        display: block;
        font-size: calc(12px * var(--font-scale, 1));
        color: #666;
        margin-bottom: 5px;
      }
    }
  }
}
</style>
