/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
	key: string;
	data: T;
	timestamp: number;
	expireTime?: number;
	version: string;
	dependencies: string[];
}

/**
 * 缓存策略配置
 */
export interface CacheStrategy {
	// 缓存过期时间（毫秒）
	expireTime?: number;
	// 最大缓存项数量
	maxItems?: number;
	// 是否持久化到本地存储
	persistent?: boolean;
	// 缓存版本（用于缓存失效）
	version?: string;
	// 依赖项（当依赖项变化时失效）
	dependencies?: string[];
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
	totalItems: number;
	memoryItems: number;
	persistentItems: number;
	hitCount: number;
	missCount: number;
	hitRate: number;
	totalSize: number;
	lastCleanup: number;
}

/**
 * 智能缓存管理器
 * 提供多层缓存、智能失效和性能优化
 */
export class CacheManager {
	private memoryCache: Map<string, CacheItem> = new Map();
	private readonly STORAGE_PREFIX = 'smart_cache_';
	private readonly STATS_KEY = 'cache_stats';
	private stats: CacheStats;
	private cleanupTimer: any = null;
	private readonly CLEANUP_INTERVAL = 5 * 60 * 1000; // 5分钟清理一次

	constructor() {
		this.stats = this.loadStats();
		this.startCleanupTimer();
		console.info('🗄️ [CACHE] 缓存管理器初始化完成');
	}

	/**
	 * 设置缓存
	 */
	set<T>(key: string, data: T, strategy: CacheStrategy = {}): void {
		const now = Date.now();
		const cacheItem: CacheItem<T> = {
			key,
			data,
			timestamp: now,
			expireTime: strategy.expireTime ? now + strategy.expireTime : undefined,
			version: strategy.version || '1.0.0',
			dependencies: strategy.dependencies || []
		};

		// 存储到内存缓存
		this.memoryCache.set(key, cacheItem);

		// 如果需要持久化，存储到本地存储
		if (strategy.persistent) {
			this.setPersistent(key, cacheItem);
		}

		// 检查缓存大小限制
		if (strategy.maxItems && this.memoryCache.size > strategy.maxItems) {
			this.evictOldestItems(strategy.maxItems);
		}

		// 更新统计
		this.stats.totalItems = this.memoryCache.size;
		this.stats.memoryItems = this.memoryCache.size;

		console.info(`🗄️ [CACHE] 设置缓存: ${key}, 持久化: ${strategy.persistent || false}`);
	}

	/**
	 * 获取缓存
	 */
	get<T>(key: string): T | null {
		// 首先从内存缓存获取
		let cacheItem = this.memoryCache.get(key);

		// 如果内存中没有，尝试从持久化存储获取
		if (!cacheItem) {
			cacheItem = this.getPersistent(key);
			if (cacheItem) {
				// 恢复到内存缓存
				this.memoryCache.set(key, cacheItem);
			}
		}

		if (!cacheItem) {
			this.stats.missCount++;
			console.info(`🗄️ [CACHE] 缓存未命中: ${key}`);
			return null;
		}

		// 检查是否过期
		if (this.isExpired(cacheItem)) {
			this.delete(key);
			this.stats.missCount++;
			console.info(`🗄️ [CACHE] 缓存已过期: ${key}`);
			return null;
		}

		this.stats.hitCount++;
		console.info(`🗄️ [CACHE] 缓存命中: ${key}`);
		return cacheItem.data as T;
	}

	/**
	 * 删除缓存
	 */
	delete(key: string): boolean {
		const deleted = this.memoryCache.delete(key);
		this.deletePersistent(key);

		if (deleted) {
			this.stats.totalItems = this.memoryCache.size;
			this.stats.memoryItems = this.memoryCache.size;
			console.info(`🗄️ [CACHE] 删除缓存: ${key}`);
		}

		return deleted;
	}

	/**
	 * 检查缓存是否存在
	 */
	has(key: string): boolean {
		if (this.memoryCache.has(key)) {
			const cacheItem = this.memoryCache.get(key)!;
			return !this.isExpired(cacheItem);
		}

		// 检查持久化存储
		const persistentItem = this.getPersistent(key);
		if (persistentItem && !this.isExpired(persistentItem)) {
			// 恢复到内存缓存
			this.memoryCache.set(key, persistentItem);
			return true;
		}

		return false;
	}

	/**
	 * 清除所有缓存
	 */
	clear(): void {
		this.memoryCache.clear();
		this.clearPersistent();
		this.stats.totalItems = 0;
		this.stats.memoryItems = 0;
		this.stats.persistentItems = 0;
		console.info('🗄️ [CACHE] 清除所有缓存');
	}

	/**
	 * 根据依赖项失效缓存
	 */
	invalidateByDependency(dependency: string): number {
		let invalidatedCount = 0;
		const keysToDelete: string[] = [];

		// 检查内存缓存
		this.memoryCache.forEach((item, key) => {
			if (item.dependencies.includes(dependency)) {
				keysToDelete.push(key);
			}
		});

		// 删除失效的缓存
		keysToDelete.forEach(key => {
			this.delete(key);
			invalidatedCount++;
		});

		console.info(`🗄️ [CACHE] 根据依赖项 "${dependency}" 失效了 ${invalidatedCount} 个缓存项`);
		return invalidatedCount;
	}

	/**
	 * 根据版本失效缓存
	 */
	invalidateByVersion(version: string): number {
		let invalidatedCount = 0;
		const keysToDelete: string[] = [];

		this.memoryCache.forEach((item, key) => {
			if (item.version !== version) {
				keysToDelete.push(key);
			}
		});

		keysToDelete.forEach(key => {
			this.delete(key);
			invalidatedCount++;
		});

		console.info(`🗄️ [CACHE] 根据版本不匹配失效了 ${invalidatedCount} 个缓存项`);
		return invalidatedCount;
	}

	/**
	 * 根据模式批量删除缓存
	 */
	deleteByPattern(pattern: RegExp): number {
		let deletedCount = 0;
		const keysToDelete: string[] = [];

		this.memoryCache.forEach((item, key) => {
			if (pattern.test(key)) {
				keysToDelete.push(key);
			}
		});

		keysToDelete.forEach(key => {
			this.delete(key);
			deletedCount++;
		});

		console.info(`🗄️ [CACHE] 根据模式删除了 ${deletedCount} 个缓存项`);
		return deletedCount;
	}

	/**
	 * 获取缓存统计信息
	 */
	getStats(): CacheStats {
		this.stats.hitRate = this.stats.hitCount + this.stats.missCount > 0
			? this.stats.hitCount / (this.stats.hitCount + this.stats.missCount)
			: 0;

		this.stats.totalSize = this.calculateCacheSize();
		this.saveStats();

		return { ...this.stats };
	}

	/**
	 * 手动清理过期缓存
	 */
	cleanup(): number {
		let cleanedCount = 0;
		const now = Date.now();
		const keysToDelete: string[] = [];

		this.memoryCache.forEach((item, key) => {
			if (this.isExpired(item)) {
				keysToDelete.push(key);
			}
		});

		keysToDelete.forEach(key => {
			this.delete(key);
			cleanedCount++;
		});

		this.stats.lastCleanup = now;
		console.info(`🗄️ [CACHE] 清理了 ${cleanedCount} 个过期缓存项`);

		return cleanedCount;
	}

	/**
	 * 预热缓存
	 */
	async warmup(keys: string[], dataLoader: (key: string) => Promise<any>): Promise<void> {
		console.info(`🗄️ [CACHE] 开始预热 ${keys.length} 个缓存项`);
		const startTime = typeof performance !== 'undefined' ? performance.now() : Date.now();

		const promises = keys.map(async (key) => {
			if (!this.has(key)) {
				try {
					const data = await dataLoader(key);
					this.set(key, data, { persistent: true });
				} catch (error) {
					console.warn(`⚠️ [CACHE] 预热缓存失败: ${key}`, error);
				}
			}
		});

		await Promise.all(promises);

		const endTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
		console.info(`🗄️ [CACHE] 缓存预热完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
	}

	/**
	 * 检查缓存项是否过期
	 */
	private isExpired(item: CacheItem): boolean {
		if (!item.expireTime) return false;
		return Date.now() > item.expireTime;
	}

	/**
	 * 淘汰最旧的缓存项
	 */
	private evictOldestItems(maxItems: number): void {
		const items = Array.from(this.memoryCache.entries());
		items.sort((a, b) => a[1].timestamp - b[1].timestamp);

		const itemsToRemove = items.length - maxItems;
		for (let i = 0; i < itemsToRemove; i++) {
			this.delete(items[i][0]);
		}

		console.info(`🗄️ [CACHE] 淘汰了 ${itemsToRemove} 个最旧的缓存项`);
	}

	/**
	 * 设置持久化缓存
	 */
	private setPersistent(key: string, item: CacheItem): void {
		try {
			const storageKey = this.STORAGE_PREFIX + key;
			uni.setStorageSync(storageKey, JSON.stringify(item));
			this.stats.persistentItems++;
		} catch (error) {
			console.warn(`⚠️ [CACHE] 设置持久化缓存失败: ${key}`, error);
		}
	}

	/**
	 * 获取持久化缓存
	 */
	private getPersistent(key: string): CacheItem | null {
		try {
			const storageKey = this.STORAGE_PREFIX + key;
			const data = uni.getStorageSync(storageKey);
			return data ? JSON.parse(data) : null;
		} catch (error) {
			console.warn(`⚠️ [CACHE] 获取持久化缓存失败: ${key}`, error);
			return null;
		}
	}

	/**
	 * 删除持久化缓存
	 */
	private deletePersistent(key: string): void {
		try {
			const storageKey = this.STORAGE_PREFIX + key;
			uni.removeStorageSync(storageKey);
		} catch (error) {
			console.warn(`⚠️ [CACHE] 删除持久化缓存失败: ${key}`, error);
		}
	}

	/**
	 * 清除所有持久化缓存
	 */
	private clearPersistent(): void {
		try {
			const info = uni.getStorageInfoSync();
			const keysToRemove = info.keys.filter(key => key.startsWith(this.STORAGE_PREFIX));
			keysToRemove.forEach(key => uni.removeStorageSync(key));
		} catch (error) {
			console.warn('⚠️ [CACHE] 清除持久化缓存失败:', error);
		}
	}

	/**
	 * 计算缓存大小
	 */
	private calculateCacheSize(): number {
		let size = 0;
		this.memoryCache.forEach(item => {
			try {
				size += JSON.stringify(item).length;
			} catch (error) {
				// 忽略无法序列化的项
			}
		});
		return size;
	}

	/**
	 * 加载统计信息
	 */
	private loadStats(): CacheStats {
		try {
			const saved = uni.getStorageSync(this.STATS_KEY);
			if (saved) {
				return JSON.parse(saved);
			}
		} catch (error) {
			console.warn('⚠️ [CACHE] 加载统计信息失败:', error);
		}

		return {
			totalItems: 0,
			memoryItems: 0,
			persistentItems: 0,
			hitCount: 0,
			missCount: 0,
			hitRate: 0,
			totalSize: 0,
			lastCleanup: Date.now()
		};
	}

	/**
	 * 保存统计信息
	 */
	private saveStats(): void {
		try {
			uni.setStorageSync(this.STATS_KEY, JSON.stringify(this.stats));
		} catch (error) {
			console.warn('⚠️ [CACHE] 保存统计信息失败:', error);
		}
	}

	/**
	 * 启动清理定时器
	 */
	private startCleanupTimer(): void {
		this.cleanupTimer = setInterval(() => {
			this.cleanup();
		}, this.CLEANUP_INTERVAL);
	}

	/**
	 * 停止清理定时器
	 */
	stopCleanupTimer(): void {
		if (this.cleanupTimer) {
			clearInterval(this.cleanupTimer);
			this.cleanupTimer = null;
		}
	}

	/**
	 * 销毁缓存管理器
	 */
	destroy(): void {
		this.stopCleanupTimer();
		this.clear();
		console.info('🗄️ [CACHE] 缓存管理器已销毁');
	}
}

// 创建全局实例
export const cacheManager = new CacheManager();
