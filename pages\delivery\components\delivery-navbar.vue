<template>
	<view class="delivery-navbar" :style="dynamicFontStyle">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

		<!-- 主导航栏 -->
		<view class="navbar-content">
			<!-- 左侧刷新按钮和总数 -->
			<view class="navbar-left">
				<view class="refresh-icon" @click="onRefreshClick">
					<text class="icon iconfont icon-refresh"></text>
				</view>
			</view>

			<!-- 中间数量显示和搜索区 -->
			<view class="navbar-center">
				<view class="center-content">
					<view v-if="!showSearchInput" class="counts-container">
						<view class="multi-line-layout">
							<text class="count-text delivery-line">{{ title }}</text>
							<text class="count-text stats-line">
								<text class="stats-label">派单:</text><text class="stats-number">{{ totalCount }}</text>
								<text class="stats-label">当前:</text><text class="stats-number">{{ currentCount || 0 }}</text>
							</text>
						</view>
					</view>
					<view v-if="!showSearchInput" class="search-icon" @click="toggleSearchInput">
						<text class="icon iconfont icon-search"></text>
					</view>

					<!-- 搜索输入框 -->
					<view v-if="showSearchInput" class="search-input-container">
						<input
							class="search-input"
							type="text"
							v-model="searchText"
							placeholder="搜索"
							confirm-type="search"
							@confirm="handleSearch"
							focus />
						<view class="search-actions">
							<view class="search-button" @click="handleSearch">
								<text class="icon iconfont icon-search"></text>
							</view>
							<view class="close-button" @click="toggleSearchInput">
								<text class="icon iconfont icon-close"></text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 右侧筛选设置区 -->
			<view class="navbar-right">
				<view class="filter-icons">
					<view class="settings-icon" @click="onSettingsClick">
						<text class="icon iconfont icon-settings"></text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';

// 声明全局 uni 对象，解决 TypeScript 错误
declare const uni: any;

// 定义组件属性
const props = defineProps({
	title: {
		type: String,
		default: '派单管理',
	},
	totalCount: {
		type: [Number, String],
		default: 0,
	},
	currentCount: {
		type: [Number, String],
		default: 0,
	},
});

// 定义事件
const emit = defineEmits(['search', 'refresh', 'settings']);

// 状态栏高度
const statusBarHeight = ref(20);

// 字体大小管理
const currentFontScale = ref(1);

// 动态字体样式
const dynamicFontStyle = computed(() => ({
	'--font-scale': currentFontScale.value
}));

// 初始化字体大小
const initFontSize = () => {
	try {
		const savedFontScale = uni.getStorageSync('fontScale') || 1;
		currentFontScale.value = savedFontScale;
		console.info(`📄 [DELIVERY-NAVBAR] 组件字体大小初始化: 缩放 ${savedFontScale}`);
	} catch (error) {
		console.error('组件字体大小初始化失败:', error);
	}
};

// 处理字体大小变化
const handleFontSizeChange = (data: any) => {
	const { size, scale } = data;
	currentFontScale.value = scale;
	console.info(`📄 [DELIVERY-NAVBAR] 组件字体大小已更新: ${size} (缩放: ${scale})`);
};

// 从本地存储加载搜索框状态
const loadSearchState = () => {
	try {
		const showSearch = uni.getStorageSync('delivery_navbar_show_search');
		const searchValue = uni.getStorageSync('delivery_navbar_search_text');
		return {
			showSearch: showSearch === 'true',
			searchText: searchValue || '',
		};
	} catch (error) {
		console.warn('加载搜索框状态失败:', error);
		return { showSearch: false, searchText: '' };
	}
};

// 保存搜索框状态到本地存储
const saveSearchState = (show: boolean, text: string) => {
	try {
		uni.setStorageSync('delivery_navbar_show_search', show.toString());
		uni.setStorageSync('delivery_navbar_search_text', text);
	} catch (error) {
		console.warn('保存搜索框状态失败:', error);
	}
};

// 加载搜索框状态
const searchState = loadSearchState();

// 是否显示搜索输入框
const showSearchInput = ref(searchState.showSearch);
// 搜索文本
const searchText = ref(searchState.searchText);

// 切换搜索输入框显示状态
const toggleSearchInput = () => {
	console.info('🔍 [DELIVERY-NAVBAR] 切换搜索框状态，当前显示:', showSearchInput.value);
	showSearchInput.value = !showSearchInput.value;
	if (!showSearchInput.value) {
		// 如果隐藏搜索框，清空搜索内容并发送空搜索
		console.info('🔍 [DELIVERY-NAVBAR] 关闭搜索框，清空搜索内容');
		searchText.value = '';
		emit('search', '');
	}
	// 保存搜索框状态
	saveSearchState(showSearchInput.value, searchText.value);
};

// 处理搜索
const handleSearch = () => {
	emit('search', searchText.value);
	// 搜索后关闭搜索框
	showSearchInput.value = false;
	// 保存搜索框状态
	saveSearchState(showSearchInput.value, searchText.value);
};

// 处理刷新按钮点击
const onRefreshClick = () => {
	emit('refresh');
};

// 处理设置按钮点击
const onSettingsClick = () => {
	console.info('🔧 [DELIVERY-NAVBAR] 设置按钮被点击');
	emit('settings');
};

// 页面加载时，获取状态栏高度和初始化字体大小
onMounted(() => {
	// 获取状态栏高度
	try {
		const sysInfo = uni.getSystemInfoSync();
		statusBarHeight.value = sysInfo.statusBarHeight || 20;
	} catch (error) {
		console.warn('获取状态栏高度失败:', error);
		statusBarHeight.value = 20; // 使用默认值
	}

	// 初始化字体大小
	initFontSize();

	// 监听字体大小变化
	uni.$on('fontSizeChanged', handleFontSizeChange);
});

// 页面卸载时的清理
onUnmounted(() => {
	// 清理字体大小监听
	uni.$off('fontSizeChanged', handleFontSizeChange);
	console.info('派单导航栏组件已卸载');
});
</script>

<style lang="scss" scoped>
.delivery-navbar {
	width: 100%;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 10000;

	// 应用字体缩放到文本元素
	.count-text, .highlight-text, .icon {
		font-size: calc(1em * var(--font-scale, 1)) !important;
	}

	.status-bar {
		background-color: #6ca5f2; // 蓝色状态栏
	}

	.navbar-content {
		height: 44px;
		background-color: #6ca5f2; // 蓝色导航栏
		display: flex;
		align-items: center;
		padding: 0 15px;

		.navbar-left {
			width: 50px;
			display: flex;
			align-items: center;
			justify-content: flex-start;

			.refresh-icon {
				width: 40px;
				height: 40px;
				display: flex;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				pointer-events: auto;

				.icon {
					color: #ffffff;
					font-weight: 700;
					font-size: 18px;
					transform: scale(1.2);
					pointer-events: none;
				}

				/* 添加点击反馈 */
				&:active {
					transform: scale(0.95);
					opacity: 0.8;
				}
			}
		}

		.navbar-center {
			flex: 1;
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 0 10px;
			min-width: 0;

			.center-content {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				flex: 1;
				min-width: 0;
			}

			.counts-container {
				display: flex;
				align-items: center;
				margin-right: 10px;
				flex: 1;
				min-width: 0;

				// 多行布局
				.multi-line-layout {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					line-height: 1.2;
					width: 100%;
					flex: 1;

					.count-text {
						color: #ffffff;
						font-weight: 600;
						text-align: center;
						margin: 1px 0;
						width: 100%;

						&.delivery-line {
							font-size: 0.9em;
							color: rgba(255, 255, 255, 0.95);
							font-weight: 700;
						}

						&.stats-line {
							display: flex;
							align-items: center;
							justify-content: center;
							font-size: 0.85em;

							.stats-label {
								color: rgba(255, 255, 255, 0.9);
								font-weight: 600;
								margin: 0 2px;
							}

							.stats-number {
								color: #ffd700;
								font-weight: 900;
								font-size: 1.1em;
								padding: 1px 4px;
								border-radius: 3px;
								margin: 0 3px;
							}
						}
					}
				}
			}

			.search-icon {
				width: 40px;
				height: 40px;
				display: flex;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				pointer-events: auto;

				.icon {
					color: #ffffff;
					font-size: 16px;
					transform: scale(1.1);
					pointer-events: none;
				}

				/* 添加点击反馈 */
				&:active {
					transform: scale(0.95);
					opacity: 0.8;
				}
			}

			.search-input-container {
				display: flex;
				flex: 1;
				height: 36px;
				background-color: transparent;
				padding: 0;
				align-items: center;
				justify-content: center;
				width: 100%;

				.search-input {
					flex: 1;
					height: 36px;
					color: #ffffff;
					background-color: transparent;
					border: none;
					border-bottom: 1px solid rgba(255, 255, 255, 0.7);
					padding: 0 5px;
				}

				.search-actions {
					display: flex;
					align-items: center;

					.search-button,
					.close-button {
						width: 36px;
						height: 36px;
						display: flex;
						justify-content: center;
						align-items: center;
						cursor: pointer;
						pointer-events: auto;

						.icon {
							color: #ffffff;
							font-size: 16px;
							transform: scale(1.1);
							pointer-events: none;
						}

						/* 添加点击反馈 */
						&:active {
							transform: scale(0.95);
							opacity: 0.8;
						}
					}
				}
			}
		}

		.navbar-right {
			width: 50px;
			display: flex;
			justify-content: flex-end;

			.filter-icons {
				display: flex;
				flex-direction: row;
				align-items: center;

				.settings-icon {
					width: 36px;
					height: 36px;
					margin-left: 5px;
					display: flex;
					justify-content: center;
					align-items: center;
					cursor: pointer;
					pointer-events: auto;
					z-index: 10;

					.icon {
						color: #ffffff;
						font-weight: 700;
						font-size: 18px;
						transform: scale(1.2);
						pointer-events: none; /* 防止图标文本阻止点击 */
					}

					/* 添加点击反馈 */
					&:active {
						transform: scale(0.95);
						opacity: 0.8;
					}
				}
			}
		}
	}
}
</style>
