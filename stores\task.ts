import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Task } from '@/types';
import { useDeliveryStore } from './delivery';
import { useImagesStore } from './images';
import { useSettingsStore } from './settings';

// 声明全局 uni 对象
declare const uni: any;

// 全局变量用于跟踪位置服务状态
let locationAvailable = true;

export const useTaskStore = defineStore('task', () => {
	// ===== 基础状态管理 =====
	// 选中的派单ID列表
	const selectedDeliveryIds = ref<string[]>([]);

	// 筛选条件
	const filters = ref({
		district: '',
		zone: 0,
		taskType: 'all',
		photoStatus: '',
		completionStatus: '',
		searchText: '',
	});

	// 本地缓存key（只保存筛选条件，不保存选中的派单ID）
	const FILTERS_KEY = 'task_filters';

	// ===== 数据访问层 - 从deliveryStore获取任务数据 =====
	// 获取所有任务数据（从deliveryStore的taskMap）
	const tasks = computed(() => {
		const deliveryStore = useDeliveryStore();
		const taskList = deliveryStore.getDeliveryTasks;
		console.log(`📋 [TASK] tasks计算触发 - 任务数量: ${taskList.length}, deliveryStore.dataVersion: ${deliveryStore.dataVersion}, deliveryStore.hasData: ${deliveryStore.hasData}`);

		// 调试：显示前几个任务的基本信息
		if (taskList.length > 0) {
			const sampleTasks = taskList.slice(0, 3).map((task: Task) => ({
				taskId: task.taskId,
				deliveryId: task.deliveryId,
				zoneName: task.zoneName || task.spotCode
			}));
			console.log(`📋 [TASK] tasks计算 - 示例任务:`, sampleTasks);
		} else {
			console.warn(`📋 [TASK] tasks计算 - 没有任务数据！`);
		}

		return taskList;
	});

	// 检查是否有数据（从deliveryStore）
	const hasData = computed(() => {
		const deliveryStore = useDeliveryStore();
		return deliveryStore.hasData;
	});

	// 检查是否正在加载（从deliveryStore）
	const loading = computed(() => {
		const deliveryStore = useDeliveryStore();
		return deliveryStore.loading;
	});

	// 检查是否正在刷新（从deliveryStore）
	const refreshing = computed(() => {
		const deliveryStore = useDeliveryStore();
		return deliveryStore.refreshing;
	});

	// ===== 本地状态管理 =====
	// 从本地存储加载状态（只加载筛选条件，不加载选中的派单ID）
	const loadStateFromStorage = () => {
		try {
			// 注意：不再从本地存储加载selectedDeliveryIds
			// selectedDeliveryIds应该由delivery页面跳转时设置，如果没有则用户需要重新选择

			// 加载筛选条件
			const savedFilters = uni.getStorageSync(FILTERS_KEY);
			if (savedFilters) {
				filters.value = JSON.parse(savedFilters);
				console.log('📋 [TASK] 从本地存储恢复筛选条件:', filters.value);
			}
		} catch (error) {
			console.warn('📋 [TASK] 加载状态失败:', error);
		}
	};

	// 保存状态到本地存储（只保存筛选条件，不保存选中的派单ID）
	const saveStateToStorage = () => {
		try {
			// 注意：不再保存selectedDeliveryIds到本地存储
			// selectedDeliveryIds是临时状态，应该由delivery页面跳转时设置

			// 保存筛选条件
			uni.setStorageSync(FILTERS_KEY, JSON.stringify(filters.value));
			console.log('📋 [TASK] 保存筛选条件到本地存储:', filters.value);
		} catch (error) {
			console.warn('📋 [TASK] 保存状态失败:', error);
		}
	};

	// 合并实景任务
	const mergeZoneTasks = (taskList: Task[]) => {
		// 从设置中获取是否需要合并实景任务
		const settingsStore = useSettingsStore();
		const shouldMergeZoneTasks = settingsStore.settings.taskDisplaySettings.mergeZoneTasks;

		// 如果不需要合并，直接返回原始任务列表
		if (!shouldMergeZoneTasks) {
			return taskList;
		}

		// 创建结果数组和分组Map
		const result: Task[] = [];
		const zoneTaskGroups = new Map<string, Task>();

		// 遍历所有任务
		taskList.forEach((task) => {
			// 如果不是实景任务（spotId不为null），直接添加到结果中
			if (task.spotId !== null) {
				result.push(task);
				return;
			}

			// 对于实景任务，根据zoneId, zoneName, zonePhotoType, zonePhotoAddress分组
			const groupKey = `${task.zoneId}_${task.zoneName}_${task.zonePhotoType || ''}_${
				task.zonePhotoAddress || ''
			}`;

			if (!zoneTaskGroups.has(groupKey)) {
				// 如果是该组的第一个任务，创建一个新的主任务
				const mainTask = { ...task, collapsedZoneTasks: [] };
				zoneTaskGroups.set(groupKey, mainTask);
				result.push(mainTask);
			} else {
				// 如果该组已存在，将任务添加到主任务的collapsedZoneTasks中
				const mainTask = zoneTaskGroups.get(groupKey)!;
				if (mainTask.collapsedZoneTasks) {
					mainTask.collapsedZoneTasks.push(task);
				}
			}
		});

		return result;
	};

	// 应用筛选条件
	const applyFilters = (taskList: Task[]) => {
		return taskList.filter((task) => {
			// 区域筛选
			if (filters.value.district && task.belongDistrict !== filters.value.district) {
				return false;
			}

			// 小区筛选
			if (filters.value.zone && task.zoneId !== filters.value.zone) {
				return false;
			}

			// 任务类型筛选
			if (filters.value.taskType && filters.value.taskType !== 'all') {
				switch (filters.value.taskType) {
					case 'spot': // 仅点位任务
						if (task.spotId === null) return false;
						break;
					case 'zone': // 仅实景任务
						if (task.spotId !== null) return false;
						break;
				}
			}

			// 拍照状态筛选
			if (filters.value.photoStatus) {
				const imagesStore = useImagesStore();
				const photoStats = imagesStore.getTaskStats(task.taskId, task.photoMax || 0);
				const required = photoStats.shouldTake || 0;
				const hasTaken = photoStats.hasTaken || 0; // 已拍 = 服务端已确认的数量
				// const hasUploaded = photoStats.hasUploaded || 0; // 已传 = 客户端临时已传，客户端上传后，立刻更新了hasTaken
				const pendingUpload = photoStats.pendingUpload || 0; // 待传 = 客户端待传
				const totalTaken = hasTaken + pendingUpload; // 总拍摄数量

				switch (filters.value.photoStatus) {
					case 'notTaken': // 仅显示未拍照
						if (totalTaken > 0) return false;
						break;
					case 'taken': // 仅显示已拍照
						if (totalTaken === 0) return false;
						break;
					case 'notComplete': // 仅显示未拍照完成
						if (totalTaken === 0 || totalTaken >= required) return false;
						break;
					case 'complete': // 仅显示已拍照完成
						if (totalTaken < required || required === 0) return false;
						break;
				}
			}

			// 完成状态筛选
			if (filters.value.completionStatus) {
				switch (filters.value.completionStatus) {
					case 'notComplete': // 仅显示未完成
						if (task.taskStatus === 'COMPLETED') return false;
						break;
					case 'complete': // 仅显示已完成
						if (task.taskStatus !== 'COMPLETED') return false;
						break;
				}
			}

			// 搜索文本筛选
			if (filters.value.searchText) {
				const searchLower = filters.value.searchText.toLowerCase();
				// 搜索任务ID
				if (task.taskId && task.taskId.toString().toLowerCase().includes(searchLower)) return true;
				// 搜索小区名称
				if (task.zoneName && task.zoneName.toLowerCase().includes(searchLower)) return true;
				// 搜索地址
				if (task.zoneAddress && task.zoneAddress.toLowerCase().includes(searchLower)) return true;
				// 搜索区域
				if (task.belongDistrict && task.belongDistrict.toLowerCase().includes(searchLower)) return true;

				// 如果没有匹配项，则过滤掉
				return false;
			}

			// 通过所有筛选条件
			return true;
		});
	};

	// 获取任务的地址字符串，用于排序
	const getTaskAddressString = (task: Task) => {
		// 构建地址层级字符串：小区 > 楼层 > 单元 > 电梯 > 位置 > 点位
		const addressParts: string[] = [];

		// 小区（最高优先级，确保按小区分组）
		if (task.zoneName) addressParts.push(task.zoneName);

		// 参考 getShotLocation 函数的实现，构建详细地址部分
		const detailParts = [
			task?.floorNo ? task.floorNo + '层' : '',
			task?.apartNo ? task.apartNo + '单元' : '',
			task?.elevatorNo ? task.elevatorNo + '号梯' : '',
			task?.position || '',
		]
			.filter((part) => part && part.trim())
			.map((part) => part.replace('层层', '层'))
			.map((part) => part.replace('单元单元', '单元'))
			.map((part) => part.replace('号梯号梯', '号梯'));

		// 将详细地址部分添加到地址数组
		if (detailParts.length > 0) {
			addressParts.push(detailParts.join(' '));
		}

		// 点位ID（实景任务没有spotId，用空字符串表示，确保在同一地址下实景任务和点位任务可以区分）
		addressParts.push(task.spotId || '');

		return addressParts.join('_');
	};

	// 对任务进行排序
	const sortTasks = (taskList: Task[]) => {
		// 获取设置 store
		const settingsStore = useSettingsStore();
		const taskSortMode = settingsStore.settings.taskDisplaySettings.taskSortMode;

		// 根据任务排列方式进行排序
		switch (taskSortMode) {
			case 'mixed':
				// 按地址从大到小混排（zoneName已经在地址字符串的开头，所以会先按小区分组）
				return taskList.sort((a, b) => {
					// 首先按小区名称排序
					const aZone = a.zoneName || '';
					const bZone = b.zoneName || '';
					const zoneCompare = aZone.localeCompare(bZone);
					if (zoneCompare !== 0) return zoneCompare;

					// 然后按照完整地址排序
					return getTaskAddressString(a).localeCompare(getTaskAddressString(b));
				});

			case 'zoneFirst':
				// 按地址从大到小先实景再点位
				return taskList.sort((a, b) => {
					// 首先按小区名称排序
					const aZone = a.zoneName || '';
					const bZone = b.zoneName || '';
					const zoneCompare = aZone.localeCompare(bZone);
					if (zoneCompare !== 0) return zoneCompare;

					// 如果小区相同，一个是实景任务，一个是点位任务，实景任务排在前面
					if (a.spotId === null && b.spotId !== null) return -1;
					if (a.spotId !== null && b.spotId === null) return 1;

					// 否则按地址排序
					return getTaskAddressString(a).localeCompare(getTaskAddressString(b));
				});

			case 'spotFirst':
				// 按地址从大到小先点位再实景
				return taskList.sort((a, b) => {
					// 首先按小区名称排序
					const aZone = a.zoneName || '';
					const bZone = b.zoneName || '';
					const zoneCompare = aZone.localeCompare(bZone);
					if (zoneCompare !== 0) return zoneCompare;

					// 如果小区相同，一个是实景任务，一个是点位任务，点位任务排在前面
					if (a.spotId === null && b.spotId !== null) return 1;
					if (a.spotId !== null && b.spotId === null) return -1;

					// 否则按地址排序
					return getTaskAddressString(a).localeCompare(getTaskAddressString(b));
				});

			default:
				return taskList;
		}
	};

	// 第二层：基于 deliveryIds 过滤后的任务列表（自动计算，包含合并和排序）
	const currentTasks = computed(() => {
		console.log(`📋 [TASK] currentTasks计算 - 选中派单ID: [${selectedDeliveryIds.value.join(', ')}], 总任务数: ${tasks.value.length}`);

		if (selectedDeliveryIds.value.length === 0) {
			// 如果没有选中的派单ID，当前任务列表为空
			console.log('📋 [TASK] currentTasks计算 - 没有选中的派单ID，返回空数组');
			return [];
		}

		// 根据选中的派单ID过滤任务，使用宽松比较解决类型不匹配问题
		let deliveryFilteredTasks = tasks.value.filter((task: Task) => {
			const isIncluded = selectedDeliveryIds.value.some((selectedId: string) => {
				// 使用 == 进行宽松比较，自动处理 string/number 转换
				return task.deliveryId == selectedId;
			});
			return isIncluded;
		});

		console.log(`📋 [TASK] currentTasks计算 - 过滤后任务数: ${deliveryFilteredTasks.length}`);

		// 调试：显示前几个任务的deliveryId
		if (deliveryFilteredTasks.length > 0) {
			const sampleTasks = deliveryFilteredTasks.slice(0, 3).map((task: Task) => ({
				taskId: task.taskId,
				deliveryId: task.deliveryId,
				zoneName: task.zoneName
			}));
			console.log('📋 [TASK] currentTasks计算 - 示例任务:', sampleTasks);
		}

		// 合并实景任务
		let processedTasks = mergeZoneTasks(deliveryFilteredTasks);
		console.log(`📋 [TASK] currentTasks计算 - 合并后任务数: ${processedTasks.length}`);

		// 对任务进行排序
		processedTasks = sortTasks(processedTasks);
		console.log(`📋 [TASK] currentTasks计算 - 排序后任务数: ${processedTasks.length}`);

		return processedTasks;
	});

	// 第三层：基于查询条件过滤的任务列表（自动计算）
	const filteredTasks = computed(() => {
		// 应用筛选条件到 currentTasks
		return applyFilters(currentTasks.value);
	});





	// ===== 统计数据计算 =====
	// 获取delivery的统计数据，支持单个deliveryId或deliveryIds数组
	const getDeliveryState = (deliveryIds: string | number | (string | number)[]) => {
		// 统一转换为数组
		const ids = Array.isArray(deliveryIds) ? deliveryIds : [deliveryIds];

		// 过滤出所有相关的任务，使用宽松比较解决类型不匹配问题
		const deliveryTasks = tasks.value.filter((task: Task) => {
			return ids.some(id => {
				// 使用 == 进行宽松比较，自动处理 string/number 转换
				return task.deliveryId == id;
			});
		});

		const zoneTasks = deliveryTasks.filter((task: Task) => task.spotId === null);
		const spotTasks = deliveryTasks.filter((task: Task) => task.spotId !== null);

		const zoneTotalCount = zoneTasks.length;
		const spotTotalCount = spotTasks.length;

		const zoneCompletedCount = zoneTasks.filter((task: Task) => task.taskStatus === 'COMPLETED').length;
		const spotCompletedCount = spotTasks.filter((task: Task) => task.taskStatus === 'COMPLETED').length;

		const zoneUncompleteCount = zoneTotalCount - zoneCompletedCount;
		const spotUncompleteCount = spotTotalCount - spotCompletedCount;

		// 计算拍照相关的统计数据
		const imagesStore = useImagesStore();
		let totalRequiredCount = 0;
		let totalUploadedCount = 0;
		let totalPendingCount = 0;

		deliveryTasks.forEach((task: Task) => {
			const photoStats = imagesStore.getTaskStats(task.taskId, task.photoMax || 0);
			totalRequiredCount += photoStats.shouldTake || 0; // 应拍
			totalUploadedCount += photoStats.hasTaken || 0;   // 已传（服务端确认）
			totalPendingCount += photoStats.pendingUpload || 0; // 待传（本地待上传）
		});

		return {
			// 实景任务
			zoneTotalCount,
			zoneCompletedCount,
			zoneUncompleteCount,

			// 点位任务
			spotTotalCount,
			spotCompletedCount,
			spotUncompleteCount,

			// 拍照统计（用于派单完成状态判断）
			requiredCount: totalRequiredCount,   // 应拍总数
			uploadedCount: totalUploadedCount,   // 已传总数
			pendingCount: totalPendingCount,     // 待传总数
		};
	};

	// ===== 数据加载方法 - 委托给deliveryStore =====
	// 加载任务列表 - 委托给 delivery store
	const loadTasks = async (forceLoad = false) => {
		const deliveryStore = useDeliveryStore();
		console.info('📋 [TASK] 委托给 delivery store 加载任务数据');
		return await deliveryStore.loadDeliveries(forceLoad);
	};

	// 刷新任务列表 - 委托给 delivery store
	const refreshTasks = async () => {
		const deliveryStore = useDeliveryStore();
		console.info('📋 [TASK] 委托给 delivery store 刷新任务数据');
		return await deliveryStore.refreshDeliveries();
	};

	// 加载单个或多个任务数据 - 从 delivery store 获取
	const loadTask = async (taskIds: string | number | (string | number)[]) => {
		try {
			// 将单个任务ID转换为数组
			const ids = Array.isArray(taskIds) ? taskIds : [taskIds];

			console.info(`📋 [TASK] 从 delivery store 获取任务数据: ${ids.join(', ')}`);

			// 任务数据已在 delivery store 中维护，无需额外处理
			// tasks computed 属性会自动从 deliveryStore.getDeliveryTasks 获取最新数据

			return true;
		} catch (error) {
			console.error('📋 [TASK] 获取任务数据失败:', error);
			return false;
		}
	};

	// 获取任务对应的派单名称
	const getTaskDeliveryName = (task: Task) => {
		const deliveryStore = useDeliveryStore();
		const { getDeliveries, getDeliveryName } = deliveryStore;
		const deliveries = getDeliveries.value;

		// 如果任务有折叠的实景任务，收集所有相关的派单
		if (task.collapsedZoneTasks && task.collapsedZoneTasks.length > 0) {
			// 收集所有相关的派单ID
			const allTasks = [task, ...task.collapsedZoneTasks];
			const deliveryIds = [...new Set(allTasks.map((t: Task) => t.deliveryId))];

			// 如果只有一个派单ID，直接返回派单名称
			if (deliveryIds.length === 1) {
				const delivery = deliveries.find((d: any) => d.deliveryId === deliveryIds[0]);
				return getDeliveryName(delivery?.deliveryId);
			}

			// 如果有多个派单ID，返回派单名称数组
			const deliveryNames = deliveryIds.map((id) => {
				const delivery = deliveries.find((d: any) => d.deliveryId === id);
				return getDeliveryName(delivery?.deliveryId);
			});

			// 返回派单名称数组，让视图层决定如何显示
			return deliveryNames;
		}

		// 如果任务没有折叠的实景任务，直接返回派单名称
		const delivery = deliveries.find((d: any) => d.deliveryId === task.deliveryId);
		return getDeliveryName(delivery?.deliveryId);
	};

	// 获取任务的所有图片（包括本地和已上传的，考虑折叠任务）
	// const getTaskImages = (taskId: string | number) => {
	// 	const imagesStore = useImagesStore();
	// 	const task = tasks.value.find((t) => t.taskId === taskId);

	// 	// 如果是主任务（有折叠任务），需要考虑所有折叠任务的图片
	// 	if (task && task.collapsedZoneTasks && task.collapsedZoneTasks.length > 0) {
	// 		// 收集所有相关任务ID
	// 		const allTaskIds = [task.taskId, ...task.collapsedZoneTasks.map((t) => t.taskId)];

	// 		// 收集所有任务的图片
	// 		let allImages: any[] = [];
	// 		for (const id of allTaskIds) {
	// 			// 获取本地图片
	// 			const localImages = imagesStore.getLocalTaskImages(id);
	// 			// 获取服务器图片
	// 			const taskWithImages = tasks.value.find((t) => t.taskId === id);
	// 			const serverImages = taskWithImages?.taskImages || [];
	// 			// 合并图片
	// 			allImages = [...allImages, ...localImages, ...serverImages];
	// 		}

	// 		return allImages;
	// 	}

	// 	// 如果是折叠任务，需要查找主任务
	// 	for (const mainTask of tasks.value) {
	// 		if (mainTask.collapsedZoneTasks && mainTask.collapsedZoneTasks.length > 0) {
	// 			const isCollapsed = mainTask.collapsedZoneTasks.some((t) => t.taskId === taskId);
	// 			if (isCollapsed) {
	// 				// 使用主任务的图片
	// 				return getTaskImages(mainTask.taskId);
	// 			}
	// 		}
	// 	}

	// 	// 如果是普通任务，直接获取
	// 	const localTaskImages = imagesStore.getLocalTaskImages(taskId);
	// 	const serverImages = task?.taskImages || [];
	// 	return [...localTaskImages, ...serverImages];
	// };

	// 计算完整地址
	const getShotLocation = (task: Task): string => {
		const addressParts = [
			task?.floorNo ? task.floorNo + '层' : '',
			task?.apartNo ? task.apartNo + '单元' : '',
			task?.elevatorNo ? task.elevatorNo + '号梯' : '',
			task?.position || '',
		]
			.filter((part) => part && part.trim())
			.map((part) => part.replace('层层', '层'))
			.map((part) => part.replace('单元单元', '单元'))
			.map((part) => part.replace('号梯号梯', '号梯'));
		return addressParts.join(' ') || '小区大门';
	};

	// 设置位置服务状态
	const setLocationAvailable = (available: boolean) => {
		locationAvailable = available;
	};

	// 获取位置服务状态
	const isLocationAvailable = () => locationAvailable;

	// 设置选中的派单ID列表
	const setSelectedDeliveryIds = (ids: string | string[]) => {
		// 如果传入的是字符串，转换为数组
		selectedDeliveryIds.value = Array.isArray(ids) ? ids : [ids];
		// currentTasks 和 filteredTasks 会自动重新计算（computed）
		// 保存到本地存储
		saveStateToStorage();
	};

	// 获取选中的派单ID列表
	const getSelectedDeliveryIds = () => selectedDeliveryIds;

	// 注意：图片管理已移至deliveryStore的imageMap和uploadMap
	// 不再在task中存储图片数据，避免多层次嵌套的复杂性

	// 更新任务状态 - 委托给 delivery store
	const updateTaskStatus = (taskId: string | number, newStatus: string) => {
		const deliveryStore = useDeliveryStore();
		console.log(`📋 [TASK] 委托给 delivery store 更新任务 ${taskId} 的状态: ${newStatus}`);
		return deliveryStore.updateTaskStatus(taskId, newStatus);
	};

	// 设置筛选条件（只影响第三层 filteredTasks）
	const setFilters = (newFilters: Partial<typeof filters.value>) => {
		// 更新筛选条件
		Object.assign(filters.value, newFilters);
		// filteredTasks 会自动重新计算（computed）
		// 保存状态到本地存储
		saveStateToStorage();
	};

	return {
		// ===== 数据访问层 =====
		// 任务数据（从deliveryStore获取）
		tasks, // 所有任务数据
		currentTasks, // 基于选中派单过滤的任务
		filteredTasks, // 基于筛选条件过滤的任务

		// 状态数据（从deliveryStore获取）
		loading, // 加载状态
		refreshing, // 刷新状态
		hasData, // 是否有数据

		// ===== 本地状态管理 =====
		filters, // 筛选条件
		selectedDeliveryIds, // 选中的派单ID列表

		// ===== 统计计算 =====
		getDeliveryState, // 获取派单统计数据

		// ===== 数据操作（委托给deliveryStore） =====
		loadTask, // 加载单个任务
		loadTasks, // 加载任务列表
		refreshTasks, // 刷新任务列表
		updateTaskStatus, // 更新任务状态
		// 注意：图片管理已移至deliveryStore，不再通过taskStore操作

		// ===== 筛选和选择 =====
		setFilters, // 设置筛选条件
		setSelectedDeliveryIds, // 设置选中的派单ID
		getSelectedDeliveryIds, // 获取选中的派单ID

		// ===== 辅助工具 =====
		getTaskDeliveryName, // 获取任务对应的派单名称
		getShotLocation, // 获取拍摄位置描述
		setLocationAvailable, // 设置位置服务状态
		isLocationAvailable, // 获取位置服务状态

		// ===== 状态持久化 =====
		loadStateFromStorage, // 从本地存储加载状态
		saveStateToStorage, // 保存状态到本地存储
	};
});
