import { AUTH_BASIC, API_URL } from './env';
import { showToast } from './toast';
import { ApiError } from './errorHandler';
import { useAuthStore } from '../stores/auth';

// 声明uni全局对象和类型，解决TypeScript错误
declare const uni: any;

// 声明UniApp命名空间
declare namespace UniApp {
  interface RequestOptions {
    url: string;
    data?: any;
    header?: any;
    method?: string;
    timeout?: number;
    dataType?: string;
    responseType?: string;
    sslVerify?: boolean;
    withCredentials?: boolean;
    firstIpv4?: boolean;
    success?: (res: any) => void;
    fail?: (err: any) => void;
    complete?: (res: any) => void;
  }

  interface UploadFileOption {
    url: string;
    filePath: string;
    name: string;
    header?: any;
    formData?: any;
    timeout?: number;
    sslVerify?: boolean;
    withCredentials?: boolean;
    success?: (res: any) => void;
    fail?: (err: any) => void;
    complete?: (res: any) => void;
    onProgressUpdate?: (res: any) => void;
  }
}

/**
 * HTTP请求方法枚举
 */
export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
  HEAD = 'HEAD',
  OPTIONS = 'OPTIONS'
}

/**
 * HTTP请求配置接口
 */
export interface HttpRequestConfig extends UniApp.RequestOptions {
  /**
   * 是否需要认证
   */
  requiresAuth?: boolean;
  /**
   * 是否自动重试认证失败的请求
   */
  autoRetryAuth?: boolean;
  /**
   * 是否防止重复请求
   */
  preventDuplicates?: boolean;
  /**
   * 自定义请求ID，用于防止重复请求
   */
  requestId?: string;
}

/**
 * HTTP上传文件配置接口
 */
export interface HttpUploadConfig extends UniApp.UploadFileOption {
  /**
   * 是否需要认证
   */
  requiresAuth?: boolean;
  /**
   * 是否自动重试认证失败的请求
   */
  autoRetryAuth?: boolean;
}

/**
 * HTTP客户端类
 * 提供HTTP请求和文件上传功能
 */
export class HttpClient {
  /**
   * 请求队列Map，用于存储正在进行的请求
   */
  private static pendingRequests = new Map<string, Promise<any>>();

  /**
   * 初始化HTTP客户端
   * 设置请求拦截器
   */
  static initialize(): void {
    // 添加请求拦截器
    uni.addInterceptor('request', {
      invoke(options: UniApp.RequestOptions) {
        // 添加API基础地址
        if (!options.url.startsWith('http')) {
          const originalUrl = options.url;
          options.url = API_URL + options.url;
          console.info(`🌐 [REQUEST] URL拼接: ${originalUrl} -> ${options.url}`);
          console.info(`🌐 [REQUEST] API_URL: ${API_URL}`);
        } else {
          console.info(`🌐 [REQUEST] 完整URL: ${options.url}`);
        }

        options.sslVerify = false;

        // 如果是oauth2/token请求，使用Basic认证
        if (options.url.includes('/oauth2/token')) {
          options.header = {
            ...options.header,
            'Authorization': AUTH_BASIC
          };
        } else {
          // 其他请求使用Bearer token认证
          const authStore = useAuthStore();
          const { token } = authStore.getAuth();
          if (token) {
            options.header = {
              ...options.header,
              'Authorization': `Bearer ${token}`
            };
          }
        }
        return options;
      },
      async complete(res: any) {
        // 处理401状态码
        if (res.statusCode === 401) {
          // 获取认证 store
          const authStore = useAuthStore();
          // 尝试获取存储的登录凭证
          const credentials = uni.getStorageSync('credentials');
          if (credentials?.username && credentials?.password) {
            try {
              // 使用统一的登录方法进行自动登录
              const loginSuccess = await authStore.login(credentials);
              if (loginSuccess) {
                // 登录成功后重试原请求
                const { token } = authStore.getAuth();
                const retryOptions = {
                  ...res.config,
                  header: {
                    ...res.config.header,
                    'Authorization': `Bearer ${token}`
                  }
                };
                return uni.request(retryOptions);
              }
            } catch (error) {
              console.error('自动登录失败:', error);
            }
          }

          // 自动登录失败，清除认证信息并跳转到登录页面
          authStore.clearAuth();
          showToast('登录已过期，请重新登录', 'none');
          // 登录页面不是tabBar页面，使用reLaunch跳转
          uni.reLaunch({
            url: '/pages/login/login'
          });
        }
        return res;
      }
    });
  }

  /**
   * 生成请求的唯一key
   * @param options 请求配置
   * @returns 请求唯一key
   */
  private static getRequestKey(options: HttpRequestConfig): string {
    if (options.requestId) {
      return options.requestId;
    }
    return `${options.method || 'GET'}_${options.url}_${JSON.stringify(options.data || {})}`;
  }

  /**
   * 发送HTTP请求
   * @param options 请求配置
   * @returns 请求结果Promise
   */
  static request<T = any>(options: HttpRequestConfig): Promise<T> {
    // 默认配置
    const defaultConfig: Partial<HttpRequestConfig> = {
      requiresAuth: true,
      autoRetryAuth: true,
      preventDuplicates: true
    };

    // 合并配置
    const config = { ...defaultConfig, ...options };

    // 如果需要防止重复请求
    if (config.preventDuplicates) {
      const requestKey = this.getRequestKey(config);

      // 如果相同的请求正在进行中，直接返回已有的Promise
      if (this.pendingRequests.has(requestKey)) {
        return this.pendingRequests.get(requestKey) as Promise<T>;
      }

      // 发起新的请求
      const requestPromise = this.executeRequest<T>(config).finally(() => {
        // 请求完成后从队列中移除
        this.pendingRequests.delete(requestKey);
      });

      // 将请求添加到队列中
      this.pendingRequests.set(requestKey, requestPromise);

      return requestPromise;
    }

    // 不需要防止重复请求，直接执行
    return this.executeRequest<T>(config);
  }

  /**
   * 执行HTTP请求
   * @param config 请求配置
   * @returns 请求结果Promise
   */
  private static executeRequest<T = any>(config: HttpRequestConfig): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      console.info(`🚀 [REQUEST] 发送请求: ${config.method || 'GET'} ${config.url}`);
      console.info(`🚀 [REQUEST] 请求配置:`, config);

      uni.request({
        ...config,
        success: (res: any) => {
          // 处理业务错误
          if (res.statusCode !== 200) {
            const errorMessage = res.data?.message || '请求失败';
            const errorCode = res.data?.code;
            reject(new ApiError(errorMessage, res.statusCode, res.data, errorCode));
            return;
          }

          resolve(res.data as T);
        },
        fail: (error: any) => {
          console.error(`❌ [REQUEST] 请求失败:`, error);
          console.error(`❌ [REQUEST] URL: ${config.url}`);

          // 分析错误类型并提供更友好的错误信息
          let errorMessage = '网络请求失败';
          let errorCode = 500;

          if (error.errMsg) {
            const errMsg = error.errMsg.toLowerCase();

            if (errMsg.includes('unable to resolve host') || errMsg.includes('no address associated with hostname')) {
              errorMessage = '无法连接到服务器，请检查网络连接或联系管理员';
              errorCode = 503; // Service Unavailable
            } else if (errMsg.includes('timeout')) {
              errorMessage = '请求超时，请检查网络连接';
              errorCode = 408; // Request Timeout
            } else if (errMsg.includes('connection refused')) {
              errorMessage = '服务器拒绝连接，请稍后重试';
              errorCode = 502; // Bad Gateway
            } else if (errMsg.includes('network error') || errMsg.includes('network is unreachable')) {
              errorMessage = '网络不可达，请检查网络设置';
              errorCode = 503; // Service Unavailable
            } else {
              errorMessage = error.errMsg;
            }
          }

          reject(new ApiError(errorMessage, errorCode, error));
        }
      });
    });
  }

  /**
   * 发送GET请求
   * @param url 请求URL
   * @param params 请求参数
   * @param config 请求配置
   * @returns 请求结果Promise
   */
  static get<T = any>(url: string, params?: any, config?: Partial<HttpRequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: HttpMethod.GET,
      data: params,
      ...config
    });
  }

  /**
   * 发送POST请求
   * @param url 请求URL
   * @param data 请求数据
   * @param config 请求配置
   * @returns 请求结果Promise
   */
  static post<T = any>(url: string, data?: any, config?: Partial<HttpRequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: HttpMethod.POST,
      data,
      ...config
    });
  }

  /**
   * 发送PUT请求
   * @param url 请求URL
   * @param data 请求数据
   * @param config 请求配置
   * @returns 请求结果Promise
   */
  static put<T = any>(url: string, data?: any, config?: Partial<HttpRequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: HttpMethod.PUT,
      data,
      ...config
    });
  }

  /**
   * 发送DELETE请求
   * @param url 请求URL
   * @param params 请求参数
   * @param config 请求配置
   * @returns 请求结果Promise
   */
  static delete<T = any>(url: string, params?: any, config?: Partial<HttpRequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: HttpMethod.DELETE,
      data: params,
      ...config
    });
  }

  /**
   * 上传文件
   * @param options 上传配置
   * @returns 上传结果Promise
   */
  static uploadFile<T = any>(options: HttpUploadConfig): Promise<T> {
    // 默认配置
    const defaultConfig: Partial<HttpUploadConfig> = {
      requiresAuth: true,
      autoRetryAuth: true
    };

    // 合并配置
    const config = { ...defaultConfig, ...options };

    // 添加API基础地址
    if (!config.url.startsWith('http')) {
      config.url = API_URL + config.url;
    }

    config.sslVerify = false;

    // 添加认证头部
    if (config.requiresAuth) {
      const authStore = useAuthStore();
      const { token } = authStore.getAuth();
      if (token) {
        config.header = {
          ...config.header,
          'Authorization': `Bearer ${token}`
        };
      }
    }

    return new Promise<T>((resolve, reject) => {
      const uploadTask = uni.uploadFile({
        ...config,
        success: async (res: any) => {
          if (res.statusCode === 401 && config.autoRetryAuth) {
            // 处理401认证错误
            const authStore = useAuthStore();
            const credentials = uni.getStorageSync('credentials');
            if (credentials?.username && credentials?.password) {
              try {
                const loginSuccess = await authStore.login(credentials);
                if (loginSuccess) {
                  // 登录成功后重试上传
                  const { token: newToken } = authStore.getAuth();
                  const retryOptions = {
                    ...config,
                    header: {
                      ...config.header,
                      'Authorization': `Bearer ${newToken}`
                    }
                  };
                  this.uploadFile<T>(retryOptions).then(resolve).catch(reject);
                  return;
                }
              } catch (error) {
                console.error('自动登录失败:', error);
                reject(new ApiError('自动登录失败', 401, error));
                return;
              }
            }

            // 自动登录失败
            reject(new ApiError('登录已过期，请重新登录', 401));
            return;
          } else if (res.statusCode !== 200) {
            // 处理其他错误
            let errorMessage: string;
            let errorData: any;

            try {
              if (typeof res.data === 'string') {
                errorData = JSON.parse(res.data);
                errorMessage = errorData.message || '上传失败';
              } else {
                errorData = res.data;
                errorMessage = '上传失败';
              }
            } catch {
              errorMessage = '上传失败';
              errorData = res.data;
            }

            reject(new ApiError(errorMessage, res.statusCode, errorData));
            return;
          }

          // 上传成功
          let responseData: any;
          try {
            // 尝试解析JSON响应
            if (typeof res.data === 'string') {
              responseData = JSON.parse(res.data);
            } else {
              responseData = res.data;
            }
            resolve(responseData);
          } catch (error) {
            console.error('解析响应数据失败:', error);
            reject(new ApiError('解析响应数据失败', res.statusCode));
          }
        },
        fail: (error: any) => {
          reject(new ApiError(error.errMsg || '上传失败', 500));
        }
      });

      // 监听上传进度
      if (config.onProgressUpdate) {
        uploadTask.onProgressUpdate(config.onProgressUpdate);
      }
    });
  }
}

// 初始化HTTP客户端
HttpClient.initialize();